{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue", "mtime": 1749362585914}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZm9yLWVhY2guanMiOwppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vdXRpbHMvaHR0cCI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQ2hhdERpYWxvZycsCiAgcHJvcHM6IHsKICAgIG1vZGVsVmFsdWU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0sCiAgICBwYXRpZW50TmFtZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgcGF0aWVudElkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGVtaXRzOiBbJ3VwZGF0ZTptb2RlbFZhbHVlJywgJ21lc3NhZ2Utc2VudCddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBtZXNzYWdlczogW10sCiAgICAgIGlucHV0TWVzc2FnZTogJycsCiAgICAgIHNlbmRpbmc6IGZhbHNlLAogICAgICByZWZyZXNoVGltZXI6IG51bGwsCiAgICAgIGZpbGVCYXNlUGF0aDogJ2h0dHA6Ly8xMjcuMC4wLjE6ODA4OC9PbmxpbmVNZWRpY2FsQXBwb2ludG1lbnRTeXN0ZW1fU2VydmVyL3VwbG9hZC8nCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIGNvbnNvbGUubG9nKCdDaGF0RGlhbG9n57uE5Lu25bey5oyC6L2977yM5paH5Lu25Z+656GA6Lev5b6EOicsIHRoaXMuZmlsZUJhc2VQYXRoKTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICB2aXNpYmxlOiB7CiAgICAgIGdldCgpIHsKICAgICAgICByZXR1cm4gdGhpcy5tb2RlbFZhbHVlOwogICAgICB9LAogICAgICBzZXQodmFsdWUpIHsKICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6bW9kZWxWYWx1ZScsIHZhbHVlKTsKICAgICAgfQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZpc2libGUobmV3VmFsKSB7CiAgICAgIGlmIChuZXdWYWwpIHsKICAgICAgICB0aGlzLmxvYWRNZXNzYWdlcygpOwogICAgICAgIHRoaXMuc3RhcnRBdXRvUmVmcmVzaCgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc3RvcEF1dG9SZWZyZXNoKCk7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWKoOi9veiBiuWkqea2iOaBrwogICAgbG9hZE1lc3NhZ2VzKCkgewogICAgICBpZiAoIXRoaXMucGF0aWVudElkKSByZXR1cm47CiAgICAgIGNvbnN0IHVzZXIgPSBKU09OLnBhcnNlKHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInVzZXIiKSk7CiAgICAgIGNvbnN0IHBhcmFtID0gewogICAgICAgIGxuYW1lOiB0aGlzLnBhdGllbnROYW1lLAogICAgICAgIGRpZDogdXNlci5kaWQKICAgICAgfTsKICAgICAgY29uc3QgdXJsID0gYmFzZSArICIvY2hhdGluZm8vbGlzdDI/Y3VycmVudFBhZ2U9MSZwYWdlU2l6ZT01MDAwIjsKICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLnJlc2RhdGEpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKCfogYrlpKnmtojmga/mlbDmja46JywgcmVzLnJlc2RhdGEpOyAvLyDosIPor5Xml6Xlv5cKICAgICAgICAgIHRoaXMubWVzc2FnZXMgPSByZXMucmVzZGF0YS5zb3J0KChhLCBiKSA9PiB7CiAgICAgICAgICAgIHJldHVybiBuZXcgRGF0ZShhLnNlbmR0aW1lKSAtIG5ldyBEYXRlKGIuc2VuZHRpbWUpOwogICAgICAgICAgfSk7CiAgICAgICAgICAvLyDmiZPljbDmr4/mnaHmtojmga/nmoTlpLTlg4/kv6Hmga8KICAgICAgICAgIHRoaXMubWVzc2FnZXMuZm9yRWFjaCgobXNnLCBpbmRleCkgPT4gewogICAgICAgICAgICBjb25zb2xlLmxvZyhg5raI5oGvJHtpbmRleCArIDF9OmAsIHsKICAgICAgICAgICAgICBmbGFnOiBtc2cuZmxhZywKICAgICAgICAgICAgICBieTE6IG1zZy5ieTEsCiAgICAgICAgICAgICAgYnkyOiBtc2cuYnkyLAogICAgICAgICAgICAgIGNvbnRlbnQ6IG1zZy5jb250ZW50CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuc2Nyb2xsVG9Cb3R0b20oKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veiBiuWkqea2iOaBr+Wksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yqg6L296IGK5aSp5raI5oGv5aSx6LSlJyk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPkemAgea2iOaBrwogICAgc2VuZE1lc3NhZ2UoKSB7CiAgICAgIGlmICghdGhpcy5pbnB1dE1lc3NhZ2UudHJpbSgpKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fovpPlhaXmtojmga/lhoXlrrknKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY29uc3QgdXNlciA9IEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgidXNlciIpKTsKICAgICAgdGhpcy5zZW5kaW5nID0gdHJ1ZTsKICAgICAgY29uc3QgcGFyYW0gPSB7CiAgICAgICAgbG5hbWU6IHRoaXMucGF0aWVudE5hbWUsCiAgICAgICAgZGlkOiB1c2VyLmRpZCwKICAgICAgICBjb250ZW50OiB0aGlzLmlucHV0TWVzc2FnZSwKICAgICAgICBmbGFnOiAiMiIsCiAgICAgICAgLy8g5Yy755Sf5Y+R6YCB55qE5raI5oGvCiAgICAgICAgc2VuZHRpbWU6IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKQogICAgICB9OwogICAgICBjb25zdCB1cmwgPSBiYXNlICsgIi9jaGF0aW5mby9hZGQiOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhbSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5zZW5kaW5nID0gZmFsc2U7CiAgICAgICAgdGhpcy5pbnB1dE1lc3NhZ2UgPSAnJzsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WPkemAgeaIkOWKnycpOwoKICAgICAgICAvLyDnq4vljbPliLfmlrDmtojmga/liJfooagKICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgIHRoaXMubG9hZE1lc3NhZ2VzKCk7CiAgICAgICAgfSwgNTAwKTsKCiAgICAgICAgLy8g6YCa55+l54i257uE5Lu25raI5oGv5bey5Y+R6YCBCiAgICAgICAgdGhpcy4kZW1pdCgnbWVzc2FnZS1zZW50Jyk7CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICB0aGlzLnNlbmRpbmcgPSBmYWxzZTsKICAgICAgICBjb25zb2xlLmVycm9yKCflj5HpgIHmtojmga/lpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPkemAgea2iOaBr+Wksei0pScpOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmu5rliqjliLDlupXpg6gKICAgIHNjcm9sbFRvQm90dG9tKCkgewogICAgICBjb25zdCBjb250YWluZXIgPSB0aGlzLiRyZWZzLmNoYXRDb250YWluZXI7CiAgICAgIGlmIChjb250YWluZXIpIHsKICAgICAgICBjb250YWluZXIuc2Nyb2xsVG9wID0gY29udGFpbmVyLnNjcm9sbEhlaWdodDsKICAgICAgfQogICAgfSwKICAgIC8vIOW8gOWni+iHquWKqOWIt+aWsAogICAgc3RhcnRBdXRvUmVmcmVzaCgpIHsKICAgICAgdGhpcy5zdG9wQXV0b1JlZnJlc2goKTsgLy8g5YWI5riF6Zmk5LmL5YmN55qE5a6a5pe25ZmoCiAgICAgIHRoaXMucmVmcmVzaFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgIHRoaXMubG9hZE1lc3NhZ2VzKCk7CiAgICAgIH0sIDUwMDApOyAvLyDmr48156eS5Yi35paw5LiA5qyhCiAgICB9LAogICAgLy8g5YGc5q2i6Ieq5Yqo5Yi35pawCiAgICBzdG9wQXV0b1JlZnJlc2goKSB7CiAgICAgIGlmICh0aGlzLnJlZnJlc2hUaW1lcikgewogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5yZWZyZXNoVGltZXIpOwogICAgICAgIHRoaXMucmVmcmVzaFRpbWVyID0gbnVsbDsKICAgICAgfQogICAgfSwKICAgIC8vIOagvOW8j+WMluaXtumXtAogICAgZm9ybWF0VGltZSh0aW1lU3RyKSB7CiAgICAgIGlmICghdGltZVN0cikgcmV0dXJuICcnOwogICAgICB0cnkgewogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lU3RyKTsKICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICAgIGNvbnN0IGRpZmYgPSBub3cgLSBkYXRlOwoKICAgICAgICAvLyDlpoLmnpzmmK/ku4rlpKkKICAgICAgICBpZiAoZGlmZiA8IDI0ICogNjAgKiA2MCAqIDEwMDAgJiYgZGF0ZS5nZXREYXRlKCkgPT09IG5vdy5nZXREYXRlKCkpIHsKICAgICAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlVGltZVN0cmluZygnemgtQ04nLCB7CiAgICAgICAgICAgIGhvdXI6ICcyLWRpZ2l0JywKICAgICAgICAgICAgbWludXRlOiAnMi1kaWdpdCcKICAgICAgICAgIH0pOwogICAgICAgIH0KCiAgICAgICAgLy8g5aaC5p6c5piv5pio5aSpCiAgICAgICAgY29uc3QgeWVzdGVyZGF5ID0gbmV3IERhdGUobm93KTsKICAgICAgICB5ZXN0ZXJkYXkuc2V0RGF0ZShub3cuZ2V0RGF0ZSgpIC0gMSk7CiAgICAgICAgaWYgKGRhdGUuZ2V0RGF0ZSgpID09PSB5ZXN0ZXJkYXkuZ2V0RGF0ZSgpKSB7CiAgICAgICAgICByZXR1cm4gJ+aYqOWkqSAnICsgZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoJ3poLUNOJywgewogICAgICAgICAgICBob3VyOiAnMi1kaWdpdCcsCiAgICAgICAgICAgIG1pbnV0ZTogJzItZGlnaXQnCiAgICAgICAgICB9KTsKICAgICAgICB9CgogICAgICAgIC8vIOWFtuS7luaXpeacnwogICAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnemgtQ04nKSArICcgJyArIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKCd6aC1DTicsIHsKICAgICAgICAgIGhvdXI6ICcyLWRpZ2l0JywKICAgICAgICAgIG1pbnV0ZTogJzItZGlnaXQnCiAgICAgICAgfSk7CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICByZXR1cm4gdGltZVN0cjsKICAgICAgfQogICAgfSwKICAgIC8vIOiOt+WPluWktOWDj1VSTAogICAgZ2V0QXZhdGFyVXJsKG1lc3NhZ2UpIHsKICAgICAgY29uc29sZS5sb2coJ+iOt+WPluWktOWDj1VSTDonLCBtZXNzYWdlKTsgLy8g6LCD6K+V5pel5b+XCiAgICAgIGlmIChtZXNzYWdlLmZsYWcgPT0gJzInKSB7CiAgICAgICAgLy8g5Yy755Sf5aS05YOPIC0g5L2/55SoYnky5a2X5q61CiAgICAgICAgaWYgKG1lc3NhZ2UuYnkyKSB7CiAgICAgICAgICBjb25zdCB1cmwgPSB0aGlzLmZpbGVCYXNlUGF0aCArIG1lc3NhZ2UuYnkyOwogICAgICAgICAgY29uc29sZS5sb2coJ+WMu+eUn+WktOWDj1VSTDonLCB1cmwpOwogICAgICAgICAgcmV0dXJuIHVybDsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5oKj6ICF5aS05YOPIC0g5L2/55SoYnkx5a2X5q61CiAgICAgICAgaWYgKG1lc3NhZ2UuYnkxKSB7CiAgICAgICAgICBjb25zdCB1cmwgPSB0aGlzLmZpbGVCYXNlUGF0aCArIG1lc3NhZ2UuYnkxOwogICAgICAgICAgY29uc29sZS5sb2coJ+aCo+iAheWktOWDj1VSTDonLCB1cmwpOwogICAgICAgICAgcmV0dXJuIHVybDsKICAgICAgICB9CiAgICAgIH0KICAgICAgLy8g5aaC5p6c5rKh5pyJ5aS05YOP77yM6L+U5Zue56m65a2X56ym5Liy77yM6K6pQ1NT6IOM5pmv6Imy5pi+56S6CiAgICAgIHJldHVybiAnJzsKICAgIH0sCiAgICAvLyDlpITnkIblm77niYfliqDovb3plJnor68KICAgIGhhbmRsZUltYWdlRXJyb3IoZXZlbnQpIHsKICAgICAgY29uc3QgaW1nID0gZXZlbnQudGFyZ2V0OwogICAgICBjb25zdCBtZXNzYWdlRWxlbWVudCA9IGltZy5jbG9zZXN0KCcubWVzc2FnZS1pdGVtJyk7CiAgICAgIGNvbnN0IGlzRG9jdG9yID0gbWVzc2FnZUVsZW1lbnQuY2xhc3NMaXN0LmNvbnRhaW5zKCdkb2N0b3ItbWVzc2FnZScpOwogICAgICBpbWcuc3JjID0gdGhpcy5nZXREZWZhdWx0QXZhdGFyKGlzRG9jdG9yID8gJ2RvY3RvcicgOiAncGF0aWVudCcpOwogICAgfSwKICAgIC8vIOiOt+WPluaCo+iAheWktOWDj1VSTO+8iOeUqOS6juiBiuWkqeWktOmDqO+8iQogICAgZ2V0UGF0aWVudEF2YXRhclVybCgpIHsKICAgICAgLy8g5LuO5pyA5paw55qE5raI5oGv5Lit6I635Y+W5oKj6ICF5aS05YOPCiAgICAgIGNvbnN0IHBhdGllbnRNZXNzYWdlID0gdGhpcy5tZXNzYWdlcy5maW5kKG1zZyA9PiBtc2cuZmxhZyA9PSAnMScpOwogICAgICBjb25zb2xlLmxvZygn5oKj6ICF5raI5oGvOicsIHBhdGllbnRNZXNzYWdlKTsgLy8g6LCD6K+V5pel5b+XCiAgICAgIGlmIChwYXRpZW50TWVzc2FnZSAmJiBwYXRpZW50TWVzc2FnZS5ieTEpIHsKICAgICAgICBjb25zdCB1cmwgPSB0aGlzLmZpbGVCYXNlUGF0aCArIHBhdGllbnRNZXNzYWdlLmJ5MTsKICAgICAgICBjb25zb2xlLmxvZygn5aS06YOo5oKj6ICF5aS05YOPVVJMOicsIHVybCk7CiAgICAgICAgcmV0dXJuIHVybDsKICAgICAgfQogICAgICByZXR1cm4gJyc7CiAgICB9LAogICAgLy8g5aSE55CG5aS06YOo5Zu+54mH5Yqg6L296ZSZ6K+vCiAgICBoYW5kbGVIZWFkZXJJbWFnZUVycm9yKGV2ZW50KSB7CiAgICAgIGV2ZW50LnRhcmdldC5zcmMgPSB0aGlzLmdldERlZmF1bHRBdmF0YXIoJ3BhdGllbnQnKTsKICAgIH0sCiAgICAvLyDlhbPpl63lr7nor53moYYKICAgIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLnN0b3BBdXRvUmVmcmVzaCgpOwogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy5pbnB1dE1lc3NhZ2UgPSAnJzsKICAgICAgdGhpcy5tZXNzYWdlcyA9IFtdOwogICAgfQogIH0sCiAgYmVmb3JlVW5tb3VudCgpIHsKICAgIHRoaXMuc3RvcEF1dG9SZWZyZXNoKCk7CiAgfQp9Ow=="}, {"version": 3, "names": ["request", "base", "name", "props", "modelValue", "type", "Boolean", "default", "patientName", "String", "patientId", "emits", "data", "messages", "inputMessage", "sending", "refreshTimer", "fileBasePath", "mounted", "console", "log", "computed", "visible", "get", "set", "value", "$emit", "watch", "newVal", "loadMessages", "startAutoRefresh", "stopAutoRefresh", "methods", "user", "JSON", "parse", "sessionStorage", "getItem", "param", "lname", "did", "url", "post", "then", "res", "resdata", "sort", "a", "b", "Date", "sendtime", "for<PERSON>ach", "msg", "index", "flag", "by1", "by2", "content", "$nextTick", "scrollToBottom", "catch", "error", "$message", "sendMessage", "trim", "warning", "toLocaleString", "success", "setTimeout", "container", "$refs", "chatContainer", "scrollTop", "scrollHeight", "setInterval", "clearInterval", "formatTime", "timeStr", "date", "now", "diff", "getDate", "toLocaleTimeString", "hour", "minute", "yesterday", "setDate", "toLocaleDateString", "e", "getAvatarUrl", "message", "handleImageError", "event", "img", "target", "messageElement", "closest", "isDoctor", "classList", "contains", "src", "getDefaultAvatar", "getPatientAvatarUrl", "patientMessage", "find", "handleHeaderImageError", "handleClose", "beforeUnmount"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"`与${patientName}的对话`\"\n    width=\"700px\"\n    :before-close=\"handleClose\"\n    class=\"chat-dialog\"\n    top=\"5vh\"\n  >\n    <!-- 聊天头部信息 -->\n    <div class=\"chat-header\">\n      <div class=\"patient-info\">\n        <div class=\"avatar\">\n          <img\n            v-if=\"getPatientAvatarUrl()\"\n            :src=\"getPatientAvatarUrl()\"\n            :alt=\"patientName\"\n            @error=\"handleHeaderImageError\"\n          />\n          <i v-else class=\"el-icon-user-solid\"></i>\n        </div>\n        <div class=\"info\">\n          <div class=\"name\">{{ patientName }}</div>\n          <div class=\"status\">在线咨询</div>\n        </div>\n      </div>\n      <div class=\"chat-actions\">\n        <el-button size=\"small\" type=\"text\" @click=\"loadMessages\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n        <el-button size=\"small\" type=\"text\" @click=\"testImages\">\n          <i class=\"el-icon-picture\"></i> 测试图片\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 聊天消息区域 -->\n    <div class=\"chat-container\" ref=\"chatContainer\">\n      <div class=\"chat-messages\">\n        <div v-if=\"messages.length === 0\" class=\"empty-messages\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          <p>暂无聊天记录</p>\n        </div>\n        <div\n          v-for=\"(message, index) in messages\"\n          :key=\"index\"\n          :class=\"['message-item', message.flag == '2' ? 'doctor-message' : 'patient-message']\"\n        >\n          <div class=\"avatar-wrapper\">\n            <div class=\"message-avatar\" :class=\"message.flag == '2' ? 'doctor-avatar' : 'patient-avatar'\">\n              <img\n                v-if=\"getAvatarUrl(message)\"\n                :src=\"getAvatarUrl(message)\"\n                :alt=\"message.flag == '2' ? '医生' : '患者'\"\n                @error=\"handleImageError\"\n              />\n              <i v-else :class=\"message.flag == '2' ? 'el-icon-s-custom' : 'el-icon-user-solid'\"></i>\n            </div>\n          </div>\n          <div class=\"message-wrapper\">\n            <div class=\"message-content\">\n              <div class=\"message-text\">{{ message.content }}</div>\n              <!-- 调试信息 -->\n              <div style=\"font-size: 10px; color: #999; margin-top: 4px;\">\n                头像: {{ message.flag == '2' ? message.by2 : message.by1 }}\n                <br>URL: {{ getAvatarUrl(message) }}\n              </div>\n            </div>\n            <div class=\"message-time\">{{ formatTime(message.sendtime) }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 输入区域 -->\n    <div class=\"chat-input\">\n      <div class=\"input-wrapper\">\n        <el-input\n          v-model=\"inputMessage\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入回复内容...\"\n          maxlength=\"500\"\n          show-word-limit\n          @keyup.ctrl.enter=\"sendMessage\"\n          class=\"message-input\"\n        ></el-input>\n        <div class=\"input-actions\">\n          <div class=\"input-tips\">\n            <span class=\"tip-item\">\n              <i class=\"el-icon-info\"></i>\n              Ctrl + Enter 快速发送\n            </span>\n            <span class=\"tip-item\">\n              <i class=\"el-icon-time\"></i>\n              每5秒自动刷新\n            </span>\n          </div>\n          <div class=\"send-actions\">\n            <el-button @click=\"inputMessage = ''\" size=\"small\">清空</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"sendMessage\"\n              :loading=\"sending\"\n              :disabled=\"!inputMessage.trim()\"\n              class=\"send-btn\"\n            >\n              <i class=\"el-icon-s-promotion\"></i>\n              发送\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport request, { base } from \"../../utils/http\";\n\nexport default {\n  name: 'ChatDialog',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    patientName: {\n      type: String,\n      default: ''\n    },\n    patientId: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['update:modelValue', 'message-sent'],\n  data() {\n    return {\n      messages: [],\n      inputMessage: '',\n      sending: false,\n      refreshTimer: null,\n      fileBasePath: 'http://127.0.0.1:8088/OnlineMedicalAppointmentSystem_Server/upload/'\n    };\n  },\n  mounted() {\n    console.log('ChatDialog组件已挂载，文件基础路径:', this.fileBasePath);\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.modelValue;\n      },\n      set(value) {\n        this.$emit('update:modelValue', value);\n      }\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        this.loadMessages();\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    }\n  },\n  methods: {\n    // 加载聊天消息\n    loadMessages() {\n      if (!this.patientId) return;\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      const param = {\n        lname: this.patientName,\n        did: user.did\n      };\n\n      const url = base + \"/chatinfo/list2?currentPage=1&pageSize=5000\";\n      request.post(url, param).then((res) => {\n        if (res.resdata) {\n          console.log('聊天消息数据:', res.resdata); // 调试日志\n          this.messages = res.resdata.sort((a, b) => {\n            return new Date(a.sendtime) - new Date(b.sendtime);\n          });\n          // 打印每条消息的头像信息\n          this.messages.forEach((msg, index) => {\n            console.log(`消息${index + 1}:`, {\n              flag: msg.flag,\n              by1: msg.by1,\n              by2: msg.by2,\n              content: msg.content\n            });\n          });\n          this.$nextTick(() => {\n            this.scrollToBottom();\n          });\n        }\n      }).catch((error) => {\n        console.error('加载聊天消息失败:', error);\n        this.$message.error('加载聊天消息失败');\n      });\n    },\n\n    // 发送消息\n    sendMessage() {\n      if (!this.inputMessage.trim()) {\n        this.$message.warning('请输入消息内容');\n        return;\n      }\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.sending = true;\n\n      const param = {\n        lname: this.patientName,\n        did: user.did,\n        content: this.inputMessage,\n        flag: \"2\", // 医生发送的消息\n        sendtime: new Date().toLocaleString()\n      };\n\n      const url = base + \"/chatinfo/add\";\n      request.post(url, param).then(() => {\n        this.sending = false;\n        this.inputMessage = '';\n        this.$message.success('发送成功');\n\n        // 立即刷新消息列表\n        setTimeout(() => {\n          this.loadMessages();\n        }, 500);\n\n        // 通知父组件消息已发送\n        this.$emit('message-sent');\n      }).catch((error) => {\n        this.sending = false;\n        console.error('发送消息失败:', error);\n        this.$message.error('发送消息失败');\n      });\n    },\n\n    // 滚动到底部\n    scrollToBottom() {\n      const container = this.$refs.chatContainer;\n      if (container) {\n        container.scrollTop = container.scrollHeight;\n      }\n    },\n\n    // 开始自动刷新\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先清除之前的定时器\n      this.refreshTimer = setInterval(() => {\n        this.loadMessages();\n      }, 5000); // 每5秒刷新一次\n    },\n\n    // 停止自动刷新\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n    },\n\n    // 格式化时间\n    formatTime(timeStr) {\n      if (!timeStr) return '';\n      try {\n        const date = new Date(timeStr);\n        const now = new Date();\n        const diff = now - date;\n\n        // 如果是今天\n        if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\n          return date.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n\n        // 如果是昨天\n        const yesterday = new Date(now);\n        yesterday.setDate(now.getDate() - 1);\n        if (date.getDate() === yesterday.getDate()) {\n          return '昨天 ' + date.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n\n        // 其他日期\n        return date.toLocaleDateString('zh-CN') + ' ' +\n               date.toLocaleTimeString('zh-CN', {\n                 hour: '2-digit',\n                 minute: '2-digit'\n               });\n      } catch (e) {\n        return timeStr;\n      }\n    },\n\n    // 获取头像URL\n    getAvatarUrl(message) {\n      console.log('获取头像URL:', message); // 调试日志\n      if (message.flag == '2') {\n        // 医生头像 - 使用by2字段\n        if (message.by2) {\n          const url = this.fileBasePath + message.by2;\n          console.log('医生头像URL:', url);\n          return url;\n        }\n      } else {\n        // 患者头像 - 使用by1字段\n        if (message.by1) {\n          const url = this.fileBasePath + message.by1;\n          console.log('患者头像URL:', url);\n          return url;\n        }\n      }\n      // 如果没有头像，返回空字符串，让CSS背景色显示\n      return '';\n    },\n\n\n\n    // 处理图片加载错误\n    handleImageError(event) {\n      const img = event.target;\n      const messageElement = img.closest('.message-item');\n      const isDoctor = messageElement.classList.contains('doctor-message');\n      img.src = this.getDefaultAvatar(isDoctor ? 'doctor' : 'patient');\n    },\n\n    // 获取患者头像URL（用于聊天头部）\n    getPatientAvatarUrl() {\n      // 从最新的消息中获取患者头像\n      const patientMessage = this.messages.find(msg => msg.flag == '1');\n      console.log('患者消息:', patientMessage); // 调试日志\n      if (patientMessage && patientMessage.by1) {\n        const url = this.fileBasePath + patientMessage.by1;\n        console.log('头部患者头像URL:', url);\n        return url;\n      }\n      return '';\n    },\n\n    // 处理头部图片加载错误\n    handleHeaderImageError(event) {\n      event.target.src = this.getDefaultAvatar('patient');\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.stopAutoRefresh();\n      this.visible = false;\n      this.inputMessage = '';\n      this.messages = [];\n    }\n  },\n  beforeUnmount() {\n    this.stopAutoRefresh();\n  }\n};\n</script>\n\n<style scoped>\n/* 对话框整体样式 */\n.chat-dialog :deep(.el-dialog) {\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.chat-dialog :deep(.el-dialog__header) {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px 24px;\n  margin: 0;\n}\n\n.chat-dialog :deep(.el-dialog__title) {\n  color: white;\n  font-weight: 600;\n  font-size: 18px;\n}\n\n.chat-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {\n  color: white;\n  font-size: 20px;\n}\n\n.chat-dialog :deep(.el-dialog__body) {\n  padding: 0;\n  background: #f8f9fa;\n}\n\n/* 聊天头部 */\n.chat-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 24px;\n  background: white;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.patient-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.patient-info .avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 18px;\n  overflow: hidden;\n  border: 2px solid #fff;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.patient-info .avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n}\n\n.patient-info .info .name {\n  font-weight: 600;\n  color: #333;\n  font-size: 16px;\n}\n\n.patient-info .info .status {\n  font-size: 12px;\n  color: #28a745;\n  margin-top: 2px;\n}\n\n.chat-actions .el-button {\n  color: #6c757d;\n}\n\n/* 聊天容器 */\n.chat-container {\n  height: 450px;\n  overflow-y: auto;\n  padding: 20px 24px;\n  background: #f8f9fa;\n}\n\n.empty-messages {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: #6c757d;\n}\n\n.empty-messages i {\n  font-size: 48px;\n  margin-bottom: 12px;\n  opacity: 0.5;\n}\n\n.empty-messages p {\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 消息列表 */\n.chat-messages {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.message-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  max-width: 85%;\n}\n\n.patient-message {\n  align-self: flex-start;\n}\n\n.doctor-message {\n  align-self: flex-end;\n  flex-direction: row-reverse;\n}\n\n/* 头像 */\n.message-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  overflow: hidden;\n  border: 2px solid #fff;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  color: white;\n  font-size: 16px;\n}\n\n.message-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n}\n\n.patient-avatar {\n  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);\n}\n\n.doctor-avatar {\n  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\n}\n\n/* 消息内容 */\n.message-wrapper {\n  flex: 1;\n  min-width: 0;\n}\n\n.message-content {\n  border-radius: 18px;\n  padding: 12px 16px;\n  position: relative;\n  word-wrap: break-word;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.patient-message .message-content {\n  background: white;\n  color: #333;\n  border-bottom-left-radius: 6px;\n}\n\n.doctor-message .message-content {\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\n  color: white;\n  border-bottom-right-radius: 6px;\n}\n\n.message-text {\n  font-size: 14px;\n  line-height: 1.5;\n  word-break: break-word;\n}\n\n.message-time {\n  font-size: 11px;\n  margin-top: 4px;\n  opacity: 0.7;\n  text-align: center;\n}\n\n.doctor-message .message-time {\n  text-align: right;\n}\n\n.patient-message .message-time {\n  text-align: left;\n}\n\n/* 输入区域 */\n.chat-input {\n  background: white;\n  border-top: 1px solid #e9ecef;\n  padding: 0;\n}\n\n.input-wrapper {\n  padding: 20px 24px;\n}\n\n.message-input :deep(.el-textarea__inner) {\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  padding: 12px 16px;\n  font-size: 14px;\n  line-height: 1.5;\n  resize: none;\n  transition: all 0.3s ease;\n}\n\n.message-input :deep(.el-textarea__inner):focus {\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.input-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 16px;\n}\n\n.input-tips {\n  display: flex;\n  gap: 16px;\n}\n\n.tip-item {\n  font-size: 12px;\n  color: #6c757d;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.tip-item i {\n  font-size: 14px;\n}\n\n.send-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.send-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 20px;\n  padding: 8px 20px;\n  font-weight: 600;\n}\n\n.send-btn:hover {\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.send-btn:disabled {\n  background: #e9ecef;\n  color: #6c757d;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 滚动条样式 */\n.chat-container::-webkit-scrollbar {\n  width: 6px;\n}\n\n.chat-container::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.chat-container::-webkit-scrollbar-thumb {\n  background: rgba(0,0,0,0.2);\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb:hover {\n  background: rgba(0,0,0,0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chat-dialog :deep(.el-dialog) {\n    width: 95% !important;\n    margin: 0 auto;\n  }\n\n  .chat-container {\n    height: 350px;\n    padding: 16px;\n  }\n\n  .input-wrapper {\n    padding: 16px;\n  }\n\n  .input-tips {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .input-actions {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .send-actions {\n    justify-content: flex-end;\n  }\n}\n</style>\n"], "mappings": ";;;AAsHA,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAEhD,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,WAAW,EAAE;MACXH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDG,SAAS,EAAE;MACTL,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAK,EAAE,CAAC,mBAAmB,EAAE,cAAc,CAAC;EAC5CC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE;IAChB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACRC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACH,YAAY,CAAC;EAC3D,CAAC;EACDI,QAAQ,EAAE;IACRC,OAAO,EAAE;MACPC,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACnB,UAAU;MACxB,CAAC;MACDoB,GAAGA,CAACC,KAAK,EAAE;QACT,IAAI,CAACC,KAAK,CAAC,mBAAmB,EAAED,KAAK,CAAC;MACxC;IACF;EACF,CAAC;EACDE,KAAK,EAAE;IACLL,OAAOA,CAACM,MAAM,EAAE;MACd,IAAIA,MAAM,EAAE;QACV,IAAI,CAACC,YAAY,CAAC,CAAC;QACnB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACzB,OAAO;QACL,IAAI,CAACC,eAAe,CAAC,CAAC;MACxB;IACF;EACF,CAAC;EACDC,OAAO,EAAE;IACP;IACAH,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC,IAAI,CAACnB,SAAS,EAAE;MAErB,MAAMuB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvD,MAAMC,KAAI,GAAI;QACZC,KAAK,EAAE,IAAI,CAAC/B,WAAW;QACvBgC,GAAG,EAAEP,IAAI,CAACO;MACZ,CAAC;MAED,MAAMC,GAAE,GAAIxC,IAAG,GAAI,6CAA6C;MAChED,OAAO,CAAC0C,IAAI,CAACD,GAAG,EAAEH,KAAK,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;QACrC,IAAIA,GAAG,CAACC,OAAO,EAAE;UACf1B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwB,GAAG,CAACC,OAAO,CAAC,EAAE;UACrC,IAAI,CAAChC,QAAO,GAAI+B,GAAG,CAACC,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACzC,OAAO,IAAIC,IAAI,CAACF,CAAC,CAACG,QAAQ,IAAI,IAAID,IAAI,CAACD,CAAC,CAACE,QAAQ,CAAC;UACpD,CAAC,CAAC;UACF;UACA,IAAI,CAACrC,QAAQ,CAACsC,OAAO,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;YACpClC,OAAO,CAACC,GAAG,CAAC,KAAKiC,KAAI,GAAI,CAAC,GAAG,EAAE;cAC7BC,IAAI,EAAEF,GAAG,CAACE,IAAI;cACdC,GAAG,EAAEH,GAAG,CAACG,GAAG;cACZC,GAAG,EAAEJ,GAAG,CAACI,GAAG;cACZC,OAAO,EAAEL,GAAG,CAACK;YACf,CAAC,CAAC;UACJ,CAAC,CAAC;UACF,IAAI,CAACC,SAAS,CAAC,MAAM;YACnB,IAAI,CAACC,cAAc,CAAC,CAAC;UACvB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACC,KAAK,CAAEC,KAAK,IAAK;QAClB1C,OAAO,CAAC0C,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,UAAU,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;IAED;IACAE,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC,IAAI,CAACjD,YAAY,CAACkD,IAAI,CAAC,CAAC,EAAE;QAC7B,IAAI,CAACF,QAAQ,CAACG,OAAO,CAAC,SAAS,CAAC;QAChC;MACF;MAEA,MAAMhC,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvD,IAAI,CAACtB,OAAM,GAAI,IAAI;MAEnB,MAAMuB,KAAI,GAAI;QACZC,KAAK,EAAE,IAAI,CAAC/B,WAAW;QACvBgC,GAAG,EAAEP,IAAI,CAACO,GAAG;QACbiB,OAAO,EAAE,IAAI,CAAC3C,YAAY;QAC1BwC,IAAI,EAAE,GAAG;QAAE;QACXJ,QAAQ,EAAE,IAAID,IAAI,CAAC,CAAC,CAACiB,cAAc,CAAC;MACtC,CAAC;MAED,MAAMzB,GAAE,GAAIxC,IAAG,GAAI,eAAe;MAClCD,OAAO,CAAC0C,IAAI,CAACD,GAAG,EAAEH,KAAK,CAAC,CAACK,IAAI,CAAC,MAAM;QAClC,IAAI,CAAC5B,OAAM,GAAI,KAAK;QACpB,IAAI,CAACD,YAAW,GAAI,EAAE;QACtB,IAAI,CAACgD,QAAQ,CAACK,OAAO,CAAC,MAAM,CAAC;;QAE7B;QACAC,UAAU,CAAC,MAAM;UACf,IAAI,CAACvC,YAAY,CAAC,CAAC;QACrB,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,IAAI,CAACH,KAAK,CAAC,cAAc,CAAC;MAC5B,CAAC,CAAC,CAACkC,KAAK,CAAEC,KAAK,IAAK;QAClB,IAAI,CAAC9C,OAAM,GAAI,KAAK;QACpBI,OAAO,CAAC0C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,QAAQ,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC;IAED;IACAF,cAAcA,CAAA,EAAG;MACf,MAAMU,SAAQ,GAAI,IAAI,CAACC,KAAK,CAACC,aAAa;MAC1C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,SAAQ,GAAIH,SAAS,CAACI,YAAY;MAC9C;IACF,CAAC;IAED;IACA3C,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MACxB,IAAI,CAACf,YAAW,GAAI0D,WAAW,CAAC,MAAM;QACpC,IAAI,CAAC7C,YAAY,CAAC,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC,EAAE;IACZ,CAAC;IAED;IACAE,eAAeA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACf,YAAY,EAAE;QACrB2D,aAAa,CAAC,IAAI,CAAC3D,YAAY,CAAC;QAChC,IAAI,CAACA,YAAW,GAAI,IAAI;MAC1B;IACF,CAAC;IAED;IACA4D,UAAUA,CAACC,OAAO,EAAE;MAClB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;MACvB,IAAI;QACF,MAAMC,IAAG,GAAI,IAAI7B,IAAI,CAAC4B,OAAO,CAAC;QAC9B,MAAME,GAAE,GAAI,IAAI9B,IAAI,CAAC,CAAC;QACtB,MAAM+B,IAAG,GAAID,GAAE,GAAID,IAAI;;QAEvB;QACA,IAAIE,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,IAAG,IAAKF,IAAI,CAACG,OAAO,CAAC,MAAMF,GAAG,CAACE,OAAO,CAAC,CAAC,EAAE;UAClE,OAAOH,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAE;YACtCC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;;QAEA;QACA,MAAMC,SAAQ,GAAI,IAAIpC,IAAI,CAAC8B,GAAG,CAAC;QAC/BM,SAAS,CAACC,OAAO,CAACP,GAAG,CAACE,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,IAAIH,IAAI,CAACG,OAAO,CAAC,MAAMI,SAAS,CAACJ,OAAO,CAAC,CAAC,EAAE;UAC1C,OAAO,KAAI,GAAIH,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAE;YAC9CC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;;QAEA;QACA,OAAON,IAAI,CAACS,kBAAkB,CAAC,OAAO,IAAI,GAAE,GACrCT,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAE;UAC/BC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;MACX,EAAE,OAAOI,CAAC,EAAE;QACV,OAAOX,OAAO;MAChB;IACF,CAAC;IAED;IACAY,YAAYA,CAACC,OAAO,EAAE;MACpBvE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsE,OAAO,CAAC,EAAE;MAClC,IAAIA,OAAO,CAACpC,IAAG,IAAK,GAAG,EAAE;QACvB;QACA,IAAIoC,OAAO,CAAClC,GAAG,EAAE;UACf,MAAMf,GAAE,GAAI,IAAI,CAACxB,YAAW,GAAIyE,OAAO,CAAClC,GAAG;UAC3CrC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEqB,GAAG,CAAC;UAC5B,OAAOA,GAAG;QACZ;MACF,OAAO;QACL;QACA,IAAIiD,OAAO,CAACnC,GAAG,EAAE;UACf,MAAMd,GAAE,GAAI,IAAI,CAACxB,YAAW,GAAIyE,OAAO,CAACnC,GAAG;UAC3CpC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEqB,GAAG,CAAC;UAC5B,OAAOA,GAAG;QACZ;MACF;MACA;MACA,OAAO,EAAE;IACX,CAAC;IAID;IACAkD,gBAAgBA,CAACC,KAAK,EAAE;MACtB,MAAMC,GAAE,GAAID,KAAK,CAACE,MAAM;MACxB,MAAMC,cAAa,GAAIF,GAAG,CAACG,OAAO,CAAC,eAAe,CAAC;MACnD,MAAMC,QAAO,GAAIF,cAAc,CAACG,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC;MACpEN,GAAG,CAACO,GAAE,GAAI,IAAI,CAACC,gBAAgB,CAACJ,QAAO,GAAI,QAAO,GAAI,SAAS,CAAC;IAClE,CAAC;IAED;IACAK,mBAAmBA,CAAA,EAAG;MACpB;MACA,MAAMC,cAAa,GAAI,IAAI,CAAC1F,QAAQ,CAAC2F,IAAI,CAACpD,GAAE,IAAKA,GAAG,CAACE,IAAG,IAAK,GAAG,CAAC;MACjEnC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEmF,cAAc,CAAC,EAAE;MACtC,IAAIA,cAAa,IAAKA,cAAc,CAAChD,GAAG,EAAE;QACxC,MAAMd,GAAE,GAAI,IAAI,CAACxB,YAAW,GAAIsF,cAAc,CAAChD,GAAG;QAClDpC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEqB,GAAG,CAAC;QAC9B,OAAOA,GAAG;MACZ;MACA,OAAO,EAAE;IACX,CAAC;IAED;IACAgE,sBAAsBA,CAACb,KAAK,EAAE;MAC5BA,KAAK,CAACE,MAAM,CAACM,GAAE,GAAI,IAAI,CAACC,gBAAgB,CAAC,SAAS,CAAC;IACrD,CAAC;IAED;IACAK,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC3E,eAAe,CAAC,CAAC;MACtB,IAAI,CAACT,OAAM,GAAI,KAAK;MACpB,IAAI,CAACR,YAAW,GAAI,EAAE;MACtB,IAAI,CAACD,QAAO,GAAI,EAAE;IACpB;EACF,CAAC;EACD8F,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC5E,eAAe,CAAC,CAAC;EACxB;AACF,CAAC", "ignoreList": []}]}