﻿<template>
    <div class="nav-header" style="background:#8BC34A;">
        <a href="/main" class="brand-logo">

   在线医疗预约系统
        </a>
      
    </div>

<div class="header" style="background:#8BC34A;">
        <div class="header-content">
            <nav class="navbar navbar-expand">
                <div class="collapse navbar-collapse justify-content-between">
                    <div class="header-left">
       
                    </div>
                    <ul class="navbar-nav header-right">
       
       
        
          <li class="nav-item dropdown notification_dropdown">
              <a class="nav-link bell dz-fullscreen"  href="javascript:void(0);" @click="toggleFullScreen">
               <svg id="icon-full" viewBox="0 0 24 24" width="20" height="20" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" class="css-i6dzq1"><path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" style="stroke-dasharray: 37, 57; stroke-dashoffset: 0;"></path></svg>
               <svg id="icon-minimize" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="A098AE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-minimize"><path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3" style="stroke-dasharray: 37, 57; stroke-dashoffset: 0;"></path></svg>
              </a>
          </li>	
          <li class="nav-item ps-3">
            <div class="dropdown header-profile2">
              <a class="nav-link" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-expanded="false" @click="toggleShowExist">
                <div class="header-info2 d-flex align-items-center">
                  <div class="header-media">
                    <img src="../assets/images/tab/1.jpg" alt="">
                  </div>
                  <div class="header-info">
                    <h6>{{userLname}}</h6>
                    <p>{{role}}</p>
                  </div>
                  
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-end" style="display: block; right: 0; top: 50;" v-show="showexist">
                <div class="card border-0 mb-0">
                  <div class="card-header py-2">
                    <div class="products">
                      <img src="../assets/images/tab/1.jpg" class="avatar avatar-md" alt="">
                      <div>
                        <h6>{{userLname}}</h6>
                        <span>{{role}}</span>	
                      </div>	
                    </div>
                  </div>
              
                  <div class="card-footer px-0 py-2" >
                    <!-- <a href="/" target="_blank" class="dropdown-item ai-icon ">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9.15722 20.7714V17.7047C9.1572 16.9246 9.79312 16.2908 10.581 16.2856H13.4671C14.2587 16.2856 14.9005 16.9209 14.9005 17.7047V17.7047V20.7809C14.9003 21.4432 15.4343 21.9845 16.103 22H18.0271C19.9451 22 21.5 20.4607 21.5 18.5618V18.5618V9.83784C21.4898 9.09083 21.1355 8.38935 20.538 7.93303L13.9577 2.6853C12.8049 1.77157 11.1662 1.77157 10.0134 2.6853L3.46203 7.94256C2.86226 8.39702 2.50739 9.09967 2.5 9.84736V18.5618C2.5 20.4607 4.05488 22 5.97291 22H7.89696C8.58235 22 9.13797 21.4499 9.13797 20.7714V20.7714" stroke="#130F26" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>                   

                      <span class="ms-2">网站首页 </span>
                    </a>    -->
                    
                    <a href="javascript:void(0);" @click="changepassword" class="dropdown-item ai-icon ">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.4232 9.4478V7.3008C16.4232 4.7878 14.3852 2.7498 11.8722 2.7498C9.35925 2.7388 7.31325 4.7668 7.30225 7.2808V7.3008V9.4478" stroke="#130F26" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.683 21.2496H8.042C5.948 21.2496 4.25 19.5526 4.25 17.4576V13.1686C4.25 11.0736 5.948 9.37659 8.042 9.37659H15.683C17.777 9.37659 19.475 11.0736 19.475 13.1686V17.4576C19.475 19.5526 17.777 21.2496 15.683 21.2496Z" stroke="#130F26" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M11.8628 14.2027V16.4237" stroke="#130F26" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>

                      <span class="ms-2">修改密码 </span>
                    </a>
                    <a href="javascript:void(0);" @click="exit" class="dropdown-item ai-icon">
                      <svg class="profle-logout" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#ff7979" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line></svg>
                      <span class="ms-2 text-danger">退出登录 </span>
                    </a>
                  </div>
                </div>
                
              </div>
            </div>
          </li>
                    </ul>
                </div>
    </nav>
  </div>
</div>


</template>
<script>
import $ from "jquery";
export default {
  data() {
    return {
      activeIndex: "1",
      activeIndex2: "1",
      showexist: false,
      userLname: "",
      role: "",
    };
  },
  mounted() {
    this.userLname = sessionStorage.getItem("userLname");
    this.role = sessionStorage.getItem("role");  

    //判断是否登录
    if(this.userLname == null){     
       this.$router.push("/");
    }

  },
  methods: {
    handleSelect(key, keyPath) {
      console.log(key, keyPath);
    },
    toggleShowExist() {    
      this.showexist = !this.showexist;

      if(this.showexist){
        $(".dropdown-menu").removeClass("show");
      }else{
        $(".dropdown-menu").addClass("show");
      }   

    },

    toggleFullScreen() {
      var elem = document.documentElement;
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if (elem.webkitRequestFullscreen) {
        elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) {
        elem.msRequestFullscreen(); 
      }
    },

    //修改密码
    changepassword(){
      this.$router.push("/password");
    },

    //退出登录  
    exit: function() {
      var _this = this;
      this.$confirm("确认退出吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          sessionStorage.removeItem("userLname");
          sessionStorage.removeItem("role");
          _this.$router.push("/");
        })
        .catch(() => {});
    },
    
  },
};
</script>

<style scoped>
.skin1{
  background: #607D8B;
}



.dropdown-menu-end{
  right: 0;
  top: 50px;
}

</style>



