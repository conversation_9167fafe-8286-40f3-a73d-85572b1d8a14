package com.service;

import com.model.Reserve;
import com.model.Mailremindertemplate;
import org.springframework.stereotype.Service;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;
import java.util.Date;

/**
 * 邮件发送服务实现类
 */
@Service
public class EmailServiceImpl implements EmailService {
    
    // 发件人邮箱
    private static final String FROM_EMAIL = "<EMAIL>";
    
    // 发件人邮箱授权码
    private static final String AUTH_CODE = "hoxowxwlcgbsfjag";
    
    // SMTP服务器
    private static final String SMTP_HOST = "smtp.qq.com";
    
    // SMTP端口
    private static final String SMTP_PORT = "587";
    
    @Override
    public boolean sendAppointmentReminder(String toEmail, Reserve reserve, Mailremindertemplate template) {
        try {
            // 构建邮件主题
            String subject = "【丽江医院预约挂号】就诊提醒";
            
            // 构建邮件内容，替换模板中的占位符
            String content = buildEmailContent(reserve, template);
            
            return sendSimpleEmail(toEmail, subject, content);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean sendSimpleEmail(String toEmail, String subject, String content) {
        try {
            // 配置邮件服务器属性
            Properties props = new Properties();
            props.put("mail.smtp.host", SMTP_HOST);
            props.put("mail.smtp.port", SMTP_PORT);
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.ssl.trust", SMTP_HOST);
            
            // 创建认证器
            Authenticator authenticator = new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(FROM_EMAIL, AUTH_CODE);
                }
            };
            
            // 创建会话
            Session session = Session.getInstance(props, authenticator);
            
            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(FROM_EMAIL));
            message.setRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
            message.setSubject(subject, "UTF-8");
            message.setContent(content, "text/html;charset=UTF-8");
            message.setSentDate(new Date());
            
            // 发送邮件
            Transport.send(message);
            
            System.out.println("邮件发送成功: " + toEmail);
            return true;
            
        } catch (Exception e) {
            System.err.println("邮件发送失败: " + toEmail + ", 错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 构建邮件内容，替换模板中的占位符
     */
    private String buildEmailContent(Reserve reserve, Mailremindertemplate template) {
        String content = template.getContent();

        // 替换模板中的占位符
        if (content != null) {
            // 替换预约日期
            content = content.replace("{预约日期}", reserve.getRdate() != null ? reserve.getRdate() : "未设置");
            // 替换预约时间
            content = content.replace("{预约时间}", reserve.getRtime() != null ? reserve.getRtime() : "未设置");
            // 替换医生姓名
            content = content.replace("{医生姓名}", reserve.getBy1() != null ? reserve.getBy1() : "未设置");
            // 替换科室名称
            content = content.replace("{科室名称}", reserve.getPname() != null ? reserve.getPname() : "未设置");
            // 替换挂号费
            content = content.replace("{挂号费}", reserve.getPmoney() != null ? reserve.getPmoney().toString() : "0");

            // 添加一些额外的占位符支持
            content = content.replace("{患者姓名}", reserve.getLname() != null ? reserve.getLname() : "");
            content = content.replace("{预约ID}", reserve.getRid() != null ? reserve.getRid().toString() : "");
        }

        return content != null ? content : "邮件内容模板为空";
    }
}
