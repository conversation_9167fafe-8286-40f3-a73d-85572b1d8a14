package com.service;

import com.model.Reserve;
import com.model.Mailremindertemplate;
import org.springframework.stereotype.Service;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;
import java.util.Date;

/**
 * 邮件发送服务实现类
 */
@Service
public class EmailServiceImpl implements EmailService {
    
    // 发件人邮箱
    private static final String FROM_EMAIL = "<EMAIL>";
    
    // 发件人邮箱授权码
    private static final String AUTH_CODE = "hoxowxwlcgbsfjag";
    
    // SMTP服务器
    private static final String SMTP_HOST = "smtp.qq.com";
    
    // SMTP端口
    private static final String SMTP_PORT = "587";
    
    @Override
    public boolean sendAppointmentReminder(String toEmail, Reserve reserve, Mailremindertemplate template) {
        try {
            System.out.println("--- EmailService: 开始发送预约提醒邮件 ---");
            System.out.println("收件人: " + toEmail);
            System.out.println("预约ID: " + (reserve != null ? reserve.getRid() : "null"));
            System.out.println("模板ID: " + (template != null ? template.getMid() : "null"));

            // 构建邮件主题
            String subject = "【丽江医院预约挂号】就诊提醒";

            // 构建邮件内容，替换模板中的占位符
            String content = buildEmailContent(reserve, template);

            System.out.println("邮件主题: " + subject);
            System.out.println("邮件内容长度: " + (content != null ? content.length() : 0));

            boolean result = sendSimpleEmail(toEmail, subject, content);
            System.out.println("邮件发送结果: " + result);

            return result;
        } catch (Exception e) {
            System.err.println("发送预约提醒邮件异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean sendSimpleEmail(String toEmail, String subject, String content) {
        try {
            System.out.println("--- EmailService: 开始发送简单邮件 ---");
            System.out.println("发件人: " + FROM_EMAIL);
            System.out.println("收件人: " + toEmail);
            System.out.println("SMTP服务器: " + SMTP_HOST + ":" + SMTP_PORT);

            // 配置邮件服务器属性
            Properties props = new Properties();
            props.put("mail.smtp.host", SMTP_HOST);
            props.put("mail.smtp.port", SMTP_PORT);
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.ssl.trust", SMTP_HOST);

            System.out.println("邮件服务器配置完成");

            // 创建认证器
            Authenticator authenticator = new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(FROM_EMAIL, AUTH_CODE);
                }
            };

            System.out.println("认证器创建完成");

            // 创建会话
            Session session = Session.getInstance(props, authenticator);

            System.out.println("邮件会话创建完成");

            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(FROM_EMAIL));
            message.setRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
            message.setSubject(subject, "UTF-8");
            message.setContent(content, "text/html;charset=UTF-8");
            message.setSentDate(new Date());

            System.out.println("邮件消息创建完成，准备发送...");

            // 发送邮件
            Transport.send(message);

            System.out.println("✓ 邮件发送成功: " + toEmail);
            return true;

        } catch (Exception e) {
            System.err.println("✗ 邮件发送失败: " + toEmail + ", 错误类型: " + e.getClass().getSimpleName() + ", 错误信息: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 构建邮件内容，替换模板中的占位符
     */
    private String buildEmailContent(Reserve reserve, Mailremindertemplate template) {
        String content = template.getContent();

        // 替换模板中的占位符
        if (content != null) {
            // 替换预约日期
            content = content.replace("{预约日期}", reserve.getRdate() != null ? reserve.getRdate() : "未设置");
            // 替换预约时间
            content = content.replace("{预约时间}", reserve.getRtime() != null ? reserve.getRtime() : "未设置");
            // 替换医生姓名
            content = content.replace("{医生姓名}", reserve.getBy1() != null ? reserve.getBy1() : "未设置");
            // 替换科室名称
            content = content.replace("{科室名称}", reserve.getPname() != null ? reserve.getPname() : "未设置");
            // 替换挂号费
            content = content.replace("{挂号费}", reserve.getPmoney() != null ? reserve.getPmoney().toString() : "0");

            // 添加一些额外的占位符支持
            content = content.replace("{患者姓名}", reserve.getLname() != null ? reserve.getLname() : "");
            content = content.replace("{预约ID}", reserve.getRid() != null ? reserve.getRid().toString() : "");
        }

        return content != null ? content : "邮件内容模板为空";
    }
}
