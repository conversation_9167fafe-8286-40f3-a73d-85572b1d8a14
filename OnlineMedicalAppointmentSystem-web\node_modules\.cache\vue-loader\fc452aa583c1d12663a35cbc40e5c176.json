{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue?vue&type=style&index=0&id=795f54f0&scoped=true&lang=css", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue", "mtime": 1749362585914}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749193686285}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749193687363}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749193686702}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue"], "names": [], "mappings": ";AAkXA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACX;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACX;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACX;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,EAAE,CAAC,EAAE,CAAC;AACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACZ;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChD;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACX;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjD;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACZ;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;AACF", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/components/ChatDialog.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"`与${patientName}的对话`\"\n    width=\"700px\"\n    :before-close=\"handleClose\"\n    class=\"chat-dialog\"\n    top=\"5vh\"\n  >\n    <!-- 聊天头部信息 -->\n    <div class=\"chat-header\">\n      <div class=\"patient-info\">\n        <div class=\"avatar\">\n          <img\n            v-if=\"getPatientAvatarUrl()\"\n            :src=\"getPatientAvatarUrl()\"\n            :alt=\"patientName\"\n            @error=\"handleHeaderImageError\"\n          />\n          <i v-else class=\"el-icon-user-solid\"></i>\n        </div>\n        <div class=\"info\">\n          <div class=\"name\">{{ patientName }}</div>\n          <div class=\"status\">在线咨询</div>\n        </div>\n      </div>\n      <div class=\"chat-actions\">\n        <el-button size=\"small\" type=\"text\" @click=\"loadMessages\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n        <el-button size=\"small\" type=\"text\" @click=\"testImages\">\n          <i class=\"el-icon-picture\"></i> 测试图片\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 聊天消息区域 -->\n    <div class=\"chat-container\" ref=\"chatContainer\">\n      <div class=\"chat-messages\">\n        <div v-if=\"messages.length === 0\" class=\"empty-messages\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          <p>暂无聊天记录</p>\n        </div>\n        <div\n          v-for=\"(message, index) in messages\"\n          :key=\"index\"\n          :class=\"['message-item', message.flag == '2' ? 'doctor-message' : 'patient-message']\"\n        >\n          <div class=\"avatar-wrapper\">\n            <div class=\"message-avatar\" :class=\"message.flag == '2' ? 'doctor-avatar' : 'patient-avatar'\">\n              <img\n                v-if=\"getAvatarUrl(message)\"\n                :src=\"getAvatarUrl(message)\"\n                :alt=\"message.flag == '2' ? '医生' : '患者'\"\n                @error=\"handleImageError\"\n              />\n              <i v-else :class=\"message.flag == '2' ? 'el-icon-s-custom' : 'el-icon-user-solid'\"></i>\n            </div>\n          </div>\n          <div class=\"message-wrapper\">\n            <div class=\"message-content\">\n              <div class=\"message-text\">{{ message.content }}</div>\n              <!-- 调试信息 -->\n              <div style=\"font-size: 10px; color: #999; margin-top: 4px;\">\n                头像: {{ message.flag == '2' ? message.by2 : message.by1 }}\n                <br>URL: {{ getAvatarUrl(message) }}\n              </div>\n            </div>\n            <div class=\"message-time\">{{ formatTime(message.sendtime) }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 输入区域 -->\n    <div class=\"chat-input\">\n      <div class=\"input-wrapper\">\n        <el-input\n          v-model=\"inputMessage\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入回复内容...\"\n          maxlength=\"500\"\n          show-word-limit\n          @keyup.ctrl.enter=\"sendMessage\"\n          class=\"message-input\"\n        ></el-input>\n        <div class=\"input-actions\">\n          <div class=\"input-tips\">\n            <span class=\"tip-item\">\n              <i class=\"el-icon-info\"></i>\n              Ctrl + Enter 快速发送\n            </span>\n            <span class=\"tip-item\">\n              <i class=\"el-icon-time\"></i>\n              每5秒自动刷新\n            </span>\n          </div>\n          <div class=\"send-actions\">\n            <el-button @click=\"inputMessage = ''\" size=\"small\">清空</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"sendMessage\"\n              :loading=\"sending\"\n              :disabled=\"!inputMessage.trim()\"\n              class=\"send-btn\"\n            >\n              <i class=\"el-icon-s-promotion\"></i>\n              发送\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport request, { base } from \"../../utils/http\";\n\nexport default {\n  name: 'ChatDialog',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    patientName: {\n      type: String,\n      default: ''\n    },\n    patientId: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['update:modelValue', 'message-sent'],\n  data() {\n    return {\n      messages: [],\n      inputMessage: '',\n      sending: false,\n      refreshTimer: null,\n      fileBasePath: 'http://127.0.0.1:8088/OnlineMedicalAppointmentSystem_Server/upload/'\n    };\n  },\n  mounted() {\n    console.log('ChatDialog组件已挂载，文件基础路径:', this.fileBasePath);\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.modelValue;\n      },\n      set(value) {\n        this.$emit('update:modelValue', value);\n      }\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        this.loadMessages();\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    }\n  },\n  methods: {\n    // 加载聊天消息\n    loadMessages() {\n      if (!this.patientId) return;\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      const param = {\n        lname: this.patientName,\n        did: user.did\n      };\n\n      const url = base + \"/chatinfo/list2?currentPage=1&pageSize=5000\";\n      request.post(url, param).then((res) => {\n        if (res.resdata) {\n          console.log('聊天消息数据:', res.resdata); // 调试日志\n          this.messages = res.resdata.sort((a, b) => {\n            return new Date(a.sendtime) - new Date(b.sendtime);\n          });\n          // 打印每条消息的头像信息\n          this.messages.forEach((msg, index) => {\n            console.log(`消息${index + 1}:`, {\n              flag: msg.flag,\n              by1: msg.by1,\n              by2: msg.by2,\n              content: msg.content\n            });\n          });\n          this.$nextTick(() => {\n            this.scrollToBottom();\n          });\n        }\n      }).catch((error) => {\n        console.error('加载聊天消息失败:', error);\n        this.$message.error('加载聊天消息失败');\n      });\n    },\n\n    // 发送消息\n    sendMessage() {\n      if (!this.inputMessage.trim()) {\n        this.$message.warning('请输入消息内容');\n        return;\n      }\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.sending = true;\n\n      const param = {\n        lname: this.patientName,\n        did: user.did,\n        content: this.inputMessage,\n        flag: \"2\", // 医生发送的消息\n        sendtime: new Date().toLocaleString()\n      };\n\n      const url = base + \"/chatinfo/add\";\n      request.post(url, param).then(() => {\n        this.sending = false;\n        this.inputMessage = '';\n        this.$message.success('发送成功');\n\n        // 立即刷新消息列表\n        setTimeout(() => {\n          this.loadMessages();\n        }, 500);\n\n        // 通知父组件消息已发送\n        this.$emit('message-sent');\n      }).catch((error) => {\n        this.sending = false;\n        console.error('发送消息失败:', error);\n        this.$message.error('发送消息失败');\n      });\n    },\n\n    // 滚动到底部\n    scrollToBottom() {\n      const container = this.$refs.chatContainer;\n      if (container) {\n        container.scrollTop = container.scrollHeight;\n      }\n    },\n\n    // 开始自动刷新\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先清除之前的定时器\n      this.refreshTimer = setInterval(() => {\n        this.loadMessages();\n      }, 5000); // 每5秒刷新一次\n    },\n\n    // 停止自动刷新\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n    },\n\n    // 格式化时间\n    formatTime(timeStr) {\n      if (!timeStr) return '';\n      try {\n        const date = new Date(timeStr);\n        const now = new Date();\n        const diff = now - date;\n\n        // 如果是今天\n        if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\n          return date.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n\n        // 如果是昨天\n        const yesterday = new Date(now);\n        yesterday.setDate(now.getDate() - 1);\n        if (date.getDate() === yesterday.getDate()) {\n          return '昨天 ' + date.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n\n        // 其他日期\n        return date.toLocaleDateString('zh-CN') + ' ' +\n               date.toLocaleTimeString('zh-CN', {\n                 hour: '2-digit',\n                 minute: '2-digit'\n               });\n      } catch (e) {\n        return timeStr;\n      }\n    },\n\n    // 获取头像URL\n    getAvatarUrl(message) {\n      console.log('获取头像URL:', message); // 调试日志\n      if (message.flag == '2') {\n        // 医生头像 - 使用by2字段\n        if (message.by2) {\n          const url = this.fileBasePath + message.by2;\n          console.log('医生头像URL:', url);\n          return url;\n        }\n      } else {\n        // 患者头像 - 使用by1字段\n        if (message.by1) {\n          const url = this.fileBasePath + message.by1;\n          console.log('患者头像URL:', url);\n          return url;\n        }\n      }\n      // 如果没有头像，返回空字符串，让CSS背景色显示\n      return '';\n    },\n\n\n\n    // 处理图片加载错误\n    handleImageError(event) {\n      const img = event.target;\n      const messageElement = img.closest('.message-item');\n      const isDoctor = messageElement.classList.contains('doctor-message');\n      img.src = this.getDefaultAvatar(isDoctor ? 'doctor' : 'patient');\n    },\n\n    // 获取患者头像URL（用于聊天头部）\n    getPatientAvatarUrl() {\n      // 从最新的消息中获取患者头像\n      const patientMessage = this.messages.find(msg => msg.flag == '1');\n      console.log('患者消息:', patientMessage); // 调试日志\n      if (patientMessage && patientMessage.by1) {\n        const url = this.fileBasePath + patientMessage.by1;\n        console.log('头部患者头像URL:', url);\n        return url;\n      }\n      return '';\n    },\n\n    // 处理头部图片加载错误\n    handleHeaderImageError(event) {\n      event.target.src = this.getDefaultAvatar('patient');\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.stopAutoRefresh();\n      this.visible = false;\n      this.inputMessage = '';\n      this.messages = [];\n    }\n  },\n  beforeUnmount() {\n    this.stopAutoRefresh();\n  }\n};\n</script>\n\n<style scoped>\n/* 对话框整体样式 */\n.chat-dialog :deep(.el-dialog) {\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.chat-dialog :deep(.el-dialog__header) {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px 24px;\n  margin: 0;\n}\n\n.chat-dialog :deep(.el-dialog__title) {\n  color: white;\n  font-weight: 600;\n  font-size: 18px;\n}\n\n.chat-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {\n  color: white;\n  font-size: 20px;\n}\n\n.chat-dialog :deep(.el-dialog__body) {\n  padding: 0;\n  background: #f8f9fa;\n}\n\n/* 聊天头部 */\n.chat-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 24px;\n  background: white;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.patient-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.patient-info .avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 18px;\n  overflow: hidden;\n  border: 2px solid #fff;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.patient-info .avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n}\n\n.patient-info .info .name {\n  font-weight: 600;\n  color: #333;\n  font-size: 16px;\n}\n\n.patient-info .info .status {\n  font-size: 12px;\n  color: #28a745;\n  margin-top: 2px;\n}\n\n.chat-actions .el-button {\n  color: #6c757d;\n}\n\n/* 聊天容器 */\n.chat-container {\n  height: 450px;\n  overflow-y: auto;\n  padding: 20px 24px;\n  background: #f8f9fa;\n}\n\n.empty-messages {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: #6c757d;\n}\n\n.empty-messages i {\n  font-size: 48px;\n  margin-bottom: 12px;\n  opacity: 0.5;\n}\n\n.empty-messages p {\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 消息列表 */\n.chat-messages {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.message-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  max-width: 85%;\n}\n\n.patient-message {\n  align-self: flex-start;\n}\n\n.doctor-message {\n  align-self: flex-end;\n  flex-direction: row-reverse;\n}\n\n/* 头像 */\n.message-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  overflow: hidden;\n  border: 2px solid #fff;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  color: white;\n  font-size: 16px;\n}\n\n.message-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n}\n\n.patient-avatar {\n  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);\n}\n\n.doctor-avatar {\n  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\n}\n\n/* 消息内容 */\n.message-wrapper {\n  flex: 1;\n  min-width: 0;\n}\n\n.message-content {\n  border-radius: 18px;\n  padding: 12px 16px;\n  position: relative;\n  word-wrap: break-word;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.patient-message .message-content {\n  background: white;\n  color: #333;\n  border-bottom-left-radius: 6px;\n}\n\n.doctor-message .message-content {\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\n  color: white;\n  border-bottom-right-radius: 6px;\n}\n\n.message-text {\n  font-size: 14px;\n  line-height: 1.5;\n  word-break: break-word;\n}\n\n.message-time {\n  font-size: 11px;\n  margin-top: 4px;\n  opacity: 0.7;\n  text-align: center;\n}\n\n.doctor-message .message-time {\n  text-align: right;\n}\n\n.patient-message .message-time {\n  text-align: left;\n}\n\n/* 输入区域 */\n.chat-input {\n  background: white;\n  border-top: 1px solid #e9ecef;\n  padding: 0;\n}\n\n.input-wrapper {\n  padding: 20px 24px;\n}\n\n.message-input :deep(.el-textarea__inner) {\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  padding: 12px 16px;\n  font-size: 14px;\n  line-height: 1.5;\n  resize: none;\n  transition: all 0.3s ease;\n}\n\n.message-input :deep(.el-textarea__inner):focus {\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.input-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 16px;\n}\n\n.input-tips {\n  display: flex;\n  gap: 16px;\n}\n\n.tip-item {\n  font-size: 12px;\n  color: #6c757d;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.tip-item i {\n  font-size: 14px;\n}\n\n.send-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.send-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 20px;\n  padding: 8px 20px;\n  font-weight: 600;\n}\n\n.send-btn:hover {\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.send-btn:disabled {\n  background: #e9ecef;\n  color: #6c757d;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 滚动条样式 */\n.chat-container::-webkit-scrollbar {\n  width: 6px;\n}\n\n.chat-container::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.chat-container::-webkit-scrollbar-thumb {\n  background: rgba(0,0,0,0.2);\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb:hover {\n  background: rgba(0,0,0,0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chat-dialog :deep(.el-dialog) {\n    width: 95% !important;\n    margin: 0 auto;\n  }\n\n  .chat-container {\n    height: 350px;\n    padding: 16px;\n  }\n\n  .input-wrapper {\n    padding: 16px;\n  }\n\n  .input-tips {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .input-actions {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .send-actions {\n    justify-content: flex-end;\n  }\n}\n</style>\n"]}]}