# 邮件日期处理逻辑说明

## 功能概述

邮件提醒系统现在支持正确的日期匹配逻辑，能够处理数据库中存储的"MM-dd"格式日期，并与邮件模板的提前天数进行准确计算。

## 日期处理逻辑

### 1. 数据库日期格式
- **存储格式**: `MM-dd`（如 "06-09"）
- **实际含义**: 月份-日期，不包含年份
- **示例**: "06-09" 表示 6月9日

### 2. 日期计算流程

```
今天日期: 2024-06-07
邮件模板提前天数: 1天
目标预约日期: 2024-06-07 + 1天 = 2024-06-08
目标日期(月日格式): 06-08
```

### 3. 匹配逻辑

1. **查询已支付预约**: 查询所有状态为"已支付"的预约记录
2. **日期标准化**: 将数据库中的"MM-dd"格式加上当前年份
3. **日期比较**: 比较完整日期是否匹配目标日期
4. **发送邮件**: 向匹配的预约记录发送邮件

## 代码实现

### 核心逻辑代码

```java
// 计算目标日期（今天 + 提前天数）
Calendar calendar = Calendar.getInstance();
calendar.add(Calendar.DAY_OF_MONTH, days);

// 获取当前年份
int currentYear = Calendar.getInstance().get(Calendar.YEAR);

// 目标日期的完整格式（yyyy-MM-dd）
String targetDateFull = new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime());

// 筛选符合日期条件的预约记录
for (Reserve reserve : allPaidReserves) {
    String reserveDate = reserve.getRdate(); // 如 "06-09"
    
    if (reserveDate != null && !reserveDate.trim().isEmpty()) {
        String fullReserveDate;
        if (reserveDate.length() == 5 && reserveDate.contains("-")) {
            // 格式如 "06-09"，加上当前年份
            fullReserveDate = currentYear + "-" + reserveDate;
        } else if (reserveDate.length() == 10) {
            // 格式如 "2024-06-09"，直接使用
            fullReserveDate = reserveDate;
        }
        
        // 比较日期是否匹配
        if (targetDateFull.equals(fullReserveDate)) {
            matchingReserves.add(reserve);
        }
    }
}
```

## 测试示例

### 示例1: 提前1天提醒

**场景设置**:
- 今天: 2024-06-07
- 邮件模板提前天数: 1天
- 数据库中的预约日期: "06-08"

**计算过程**:
1. 目标日期 = 2024-06-07 + 1天 = 2024-06-08
2. 数据库日期标准化 = 2024 + "-" + "06-08" = "2024-06-08"
3. 匹配结果: "2024-06-08" == "2024-06-08" ✅ 匹配成功

### 示例2: 提前3天提醒

**场景设置**:
- 今天: 2024-06-07
- 邮件模板提前天数: 3天
- 数据库中的预约日期: "06-10"

**计算过程**:
1. 目标日期 = 2024-06-07 + 3天 = 2024-06-10
2. 数据库日期标准化 = 2024 + "-" + "06-10" = "2024-06-10"
3. 匹配结果: "2024-06-10" == "2024-06-10" ✅ 匹配成功

## 日志输出示例

```
=== 开始执行邮件提醒任务: 2024-06-07 10:30:00 ===
查询到邮件模板数量: 1
模板信息 - ID: 4, 提前天数: 1, 内容长度: 1500
--- 开始处理模板 ---
模板ID: 4
提前天数: 1
今天日期: 2024-06-07
目标预约日期(完整): 2024-06-08
目标预约日期(月日): 06-08
查询条件 - 状态: 已支付
查询到已支付预约记录: 5 条
开始筛选符合日期条件的预约记录:
  检查预约 - ID: 123, 用户: user003, 原始日期: 06-08, 完整日期: 2024-06-08, 目标日期: 2024-06-08
  ✓ 日期匹配，加入发送列表
  检查预约 - ID: 124, 用户: user004, 原始日期: 06-09, 完整日期: 2024-06-09, 目标日期: 2024-06-08
  ✗ 日期不匹配
筛选结果: 找到 1 条符合条件的预约记录
--- 开始发送邮件 ---
预约ID: 123, 用户名: user003
查询用户结果: 1 条记录
用户邮箱: <EMAIL>
准备发送邮件到: <EMAIL>
✓ 邮件发送成功 - 用户: user003, 邮箱: <EMAIL>, 预约日期: 06-08
=== 邮件提醒任务执行完成: 2024-06-07 10:30:05 ===
```

## 测试方法

### 1. 使用测试页面
访问 `/email-test.html`，使用"测试特定日期的邮件发送"功能：
- 输入目标日期: "06-08" 或 "2024-06-08"
- 输入模板ID: 4
- 点击"测试日期邮件"

### 2. API测试
```
POST /api/email/test-date
Content-Type: application/x-www-form-urlencoded

targetDate=06-08&templateId=4
```

### 3. 登录触发测试
任何用户登录都会自动触发邮件检查和发送。

## 注意事项

1. **年份处理**: 系统自动使用当前年份，不支持跨年预约提醒
2. **日期格式**: 支持 "MM-dd" 和 "yyyy-MM-dd" 两种格式
3. **时区**: 使用服务器本地时区进行日期计算
4. **匹配精度**: 精确到日期，不考虑具体时间

## 故障排除

### 1. 找不到匹配记录
- 检查预约状态是否为"已支付"
- 检查预约日期格式是否正确
- 检查日期计算是否准确

### 2. 日期格式错误
- 确保数据库中日期格式为"MM-dd"
- 检查是否有空值或格式异常的日期

### 3. 邮件发送失败
- 检查用户是否有邮箱地址
- 检查邮件服务配置是否正确
