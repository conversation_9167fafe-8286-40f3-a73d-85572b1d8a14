﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
        <el-form :model="formData" label-width="20%" align="left">
            <el-form-item label="用户名">
                {{ formData.lname }}</el-form-item>
            <el-form-item label="登录密码">
                {{ formData.upassword }}</el-form-item>
            <el-form-item label="姓名">
                {{ formData.uname }}</el-form-item>
            <el-form-item label="性别">
                {{ formData.usex }}</el-form-item>
            <el-form-item label="手机号码">
                {{ formData.uphone }}</el-form-item>
            <el-form-item label="电子邮箱">
                {{ formData.email }}</el-form-item>
            <el-form-item label="家庭地址">
                {{ formData.address }}</el-form-item>
            <el-form-item label="照片" prop="pic">
                <img :src="'http://localhost:8088/OnlineMedicalAppointmentSystem_Server/upload/' + formData.pic"
                    style="width: 150px;height: 150px" />
            </el-form-item>
            <el-form-item label="注册时间">
                {{ formData.utime }}</el-form-item>
            <el-form-item>
                <el-button type="info" size="small" @click="back" icon="el-icon-back">返 回</el-button>
            </el-form-item>
        </el-form>


    </div>
</template>

<script>

import request, { base } from "../../../../utils/http";
export default {
    name: 'UsersDetail',
    components: {
    },
    data() {
        return {
            id: '',
            formData: {}, //表单数据         

        };
    },
    created() {
        this.id = this.$route.query.id; //获取参数
        this.getDatas();
    },


    methods: {

        //获取列表数据
        getDatas() {
            let para = {
            };
            this.listLoading = true;
            let url = base + "/users/get?id=" + this.id;
            request.post(url, para).then((res) => {
                this.formData = JSON.parse(JSON.stringify(res.resdata));
                this.listLoading = false;
            });
        },

        // 返回
        back() {
            //返回上一页
            this.$router.go(-1);
        },

    },
}

</script>
<style scoped></style>
