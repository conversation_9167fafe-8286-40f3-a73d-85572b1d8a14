<view class="container">
    <diy-navbar bgColor="green">
        <view slot="content">在线医疗预约系统</view>
    </diy-navbar>
    <view class="container container10021">
        <view class="flex flex-wrap diy-col-24 flex-direction-column items-center flex7-clz">
            <image src="/assets/images/favicon.png" class="image3-size diy-image diy-col-0 image3-clz" mode="widthFix">
            </image>
            <view class="diy-col-0 text1-clz">在线医疗预约系统</view>
        </view>
        <form bindsubmit="submitForm" class="flex diy-form flex-direction-column diy-col-24">
            <view class="diy-form-item diy-col-24 phone-clz">
                <view class="input">
                    <text open-type="getPhoneNumber" class="diy-text-lg diy-icon-my" style="color: #636363"></text>
                    <input class="flex1" name="lname" comfirm-type="done" type="text" data-key="form.phone"
                        bindchange="changeValue" placeholder="请输入帐号" />
                </view>
            </view>
            <view class="diy-form-item diy-col-24 password-clz">
                <view class="input">
                    <text class="diy-text-lg diy-icon-lock" style="color: #636363"></text>
                    <input class="flex1" name="pass" comfirm-type="done" type="password" data-key="form.password"
                        placeholder="请输入密码" />
                </view>
            </view>

            <button form-type="submit" class="diy-col-24 green text4-clz" style="padding-bottom: 0px;padding-top: 0px;">
                登录
            </button>
            <view class="flex diy-col-24 justify-between flex-nowrap flex1-clz">
                <view class="flex flex-wrap diy-col-0" catchtap="navigateTo" data-type="page"
                    data-url="/pages/reg/index">
                    <view class="diy-col-0">还没有账号？</view>
                    <view class="diy-col-0 text2-clz">注册</view>
                </view>
            </view>
        </form>
        <view class="clearfix"></view>
    </view>
    <view class="clearfix"></view>
</view>