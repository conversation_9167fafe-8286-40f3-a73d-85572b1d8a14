<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c5aa0;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #2c5aa0;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #1e3d6f;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>邮件功能测试页面</h1>
        
        <!-- 邮件服务状态检查 -->
        <div class="test-section">
            <h3>1. 邮件服务状态检查</h3>
            <p>检查邮件服务是否正常运行</p>
            <button onclick="checkEmailStatus()">检查状态</button>
            <div id="statusResult" class="result"></div>
        </div>
        
        <!-- 发送测试邮件 -->
        <div class="test-section">
            <h3>2. 发送测试邮件</h3>
            <p>向指定邮箱发送测试邮件</p>
            <div class="form-group">
                <label for="testEmail">收件人邮箱:</label>
                <input type="email" id="testEmail" placeholder="请输入邮箱地址">
            </div>
            <button onclick="sendTestEmail()">发送测试邮件</button>
            <div id="testEmailResult" class="result"></div>
        </div>
        
        <!-- 手动触发邮件提醒任务 -->
        <div class="test-section">
            <h3>3. 手动触发邮件提醒任务</h3>
            <p>立即执行一次邮件提醒任务，向符合条件的患者发送提醒邮件</p>
            <button onclick="triggerEmailReminder()">执行邮件提醒任务</button>
            <div id="reminderResult" class="result"></div>
        </div>

        <!-- 测试特定模板 -->
        <div class="test-section">
            <h3>4. 测试特定模板邮件发送</h3>
            <p>测试指定模板ID的邮件发送功能</p>
            <div class="form-group">
                <label for="templateId">模板ID:</label>
                <input type="number" id="templateId" placeholder="请输入模板ID (例如: 4)" value="4">
            </div>
            <button onclick="testTemplateEmail()">测试模板邮件</button>
            <div id="templateResult" class="result"></div>
        </div>

        <!-- 测试用户邮件 -->
        <div class="test-section">
            <h3>5. 测试向指定用户发送邮件</h3>
            <p>向指定用户发送邮件提醒</p>
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" placeholder="请输入用户名 (例如: user003)">
            </div>
            <div class="form-group">
                <label for="userTemplateId">模板ID:</label>
                <input type="number" id="userTemplateId" placeholder="请输入模板ID (例如: 4)" value="4">
            </div>
            <button onclick="testUserEmail()">发送用户邮件</button>
            <div id="userResult" class="result"></div>
        </div>
        
        <!-- 使用说明 -->
        <div class="test-section">
            <h3>使用说明</h3>
            <ul>
                <li><strong>邮件服务状态检查</strong>: 验证邮件服务是否正常启动</li>
                <li><strong>发送测试邮件</strong>: 向指定邮箱发送测试邮件，验证邮件发送功能</li>
                <li><strong>手动触发邮件提醒任务</strong>: 立即执行一次定时任务，向符合条件的患者发送提醒邮件</li>
                <li><strong>测试特定模板邮件发送</strong>: 测试指定模板ID的邮件发送，会查找符合条件的预约记录</li>
                <li><strong>测试向指定用户发送邮件</strong>: 向指定用户名的患者发送邮件提醒</li>
            </ul>
            <p><strong>注意</strong>: 请确保已正确配置邮件模板和患者邮箱信息。</p>
            <p><strong>调试提示</strong>: 所有测试功能都会在服务器控制台输出详细的调试日志，请查看控制台了解执行详情。</p>
        </div>
    </div>

    <script>
        // 检查邮件服务状态
        function checkEmailStatus() {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在检查邮件服务状态...';
            
            fetch('/api/email/status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✓ 邮件服务运行正常';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '✗ 邮件服务异常: ' + (data.msg || '未知错误');
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请求失败: ' + error.message;
            });
        }
        
        // 发送测试邮件
        function sendTestEmail() {
            const email = document.getElementById('testEmail').value;
            const resultDiv = document.getElementById('testEmailResult');
            
            if (!email) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请输入邮箱地址';
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在发送测试邮件...';
            
            fetch('/api/email/send-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: 'email=' + encodeURIComponent(email)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✓ 测试邮件发送成功，请检查邮箱';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '✗ 测试邮件发送失败: ' + (data.msg || '未知错误');
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请求失败: ' + error.message;
            });
        }
        
        // 触发邮件提醒任务
        function triggerEmailReminder() {
            const resultDiv = document.getElementById('reminderResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在执行邮件提醒任务，请稍候...';

            fetch('/api/email/trigger-reminder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✓ 邮件提醒任务执行完成，请查看服务器控制台日志';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '✗ 邮件提醒任务执行失败: ' + (data.msg || '未知错误');
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请求失败: ' + error.message;
            });
        }

        // 测试模板邮件
        function testTemplateEmail() {
            const templateId = document.getElementById('templateId').value;
            const resultDiv = document.getElementById('templateResult');

            if (!templateId) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请输入模板ID';
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在测试模板邮件...';

            fetch('/api/email/test-template', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: 'templateId=' + encodeURIComponent(templateId)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✓ 模板邮件测试完成，请查看服务器控制台日志';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '✗ 模板邮件测试失败: ' + (data.msg || '未知错误');
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请求失败: ' + error.message;
            });
        }

        // 测试用户邮件
        function testUserEmail() {
            const username = document.getElementById('username').value;
            const templateId = document.getElementById('userTemplateId').value;
            const resultDiv = document.getElementById('userResult');

            if (!username) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请输入用户名';
                return;
            }

            if (!templateId) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请输入模板ID';
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在测试用户邮件...';

            fetch('/api/email/test-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: 'username=' + encodeURIComponent(username) + '&templateId=' + encodeURIComponent(templateId)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✓ 用户邮件测试完成，请查看服务器控制台日志';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '✗ 用户邮件测试失败: ' + (data.msg || '未知错误');
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请求失败: ' + error.message;
            });
        }
    </script>
</body>
</html>
