<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c5aa0;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #2c5aa0;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #1e3d6f;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>邮件功能测试页面</h1>
        
        <!-- 邮件服务状态检查 -->
        <div class="test-section">
            <h3>1. 邮件服务状态检查</h3>
            <p>检查邮件服务是否正常运行</p>
            <button onclick="checkEmailStatus()">检查状态</button>
            <div id="statusResult" class="result"></div>
        </div>
        
        <!-- 发送测试邮件 -->
        <div class="test-section">
            <h3>2. 发送测试邮件</h3>
            <p>向指定邮箱发送测试邮件</p>
            <div class="form-group">
                <label for="testEmail">收件人邮箱:</label>
                <input type="email" id="testEmail" placeholder="请输入邮箱地址">
            </div>
            <button onclick="sendTestEmail()">发送测试邮件</button>
            <div id="testEmailResult" class="result"></div>
        </div>
        
        <!-- 手动触发邮件提醒任务 -->
        <div class="test-section">
            <h3>3. 手动触发邮件提醒任务</h3>
            <p>立即执行一次邮件提醒任务，向符合条件的患者发送提醒邮件</p>
            <button onclick="triggerEmailReminder()">执行邮件提醒任务</button>
            <div id="reminderResult" class="result"></div>
        </div>
        
        <!-- 使用说明 -->
        <div class="test-section">
            <h3>使用说明</h3>
            <ul>
                <li><strong>邮件服务状态检查</strong>: 验证邮件服务是否正常启动</li>
                <li><strong>发送测试邮件</strong>: 向指定邮箱发送测试邮件，验证邮件发送功能</li>
                <li><strong>手动触发邮件提醒任务</strong>: 立即执行一次定时任务，向符合条件的患者发送提醒邮件</li>
            </ul>
            <p><strong>注意</strong>: 请确保已正确配置邮件模板和患者邮箱信息。</p>
        </div>
    </div>

    <script>
        // 检查邮件服务状态
        function checkEmailStatus() {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在检查邮件服务状态...';
            
            fetch('/api/email/status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✓ 邮件服务运行正常';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '✗ 邮件服务异常: ' + (data.msg || '未知错误');
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请求失败: ' + error.message;
            });
        }
        
        // 发送测试邮件
        function sendTestEmail() {
            const email = document.getElementById('testEmail').value;
            const resultDiv = document.getElementById('testEmailResult');
            
            if (!email) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请输入邮箱地址';
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在发送测试邮件...';
            
            fetch('/api/email/send-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: 'email=' + encodeURIComponent(email)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✓ 测试邮件发送成功，请检查邮箱';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '✗ 测试邮件发送失败: ' + (data.msg || '未知错误');
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请求失败: ' + error.message;
            });
        }
        
        // 触发邮件提醒任务
        function triggerEmailReminder() {
            const resultDiv = document.getElementById('reminderResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '正在执行邮件提醒任务，请稍候...';
            
            fetch('/api/email/trigger-reminder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '✓ 邮件提醒任务执行完成';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '✗ 邮件提醒任务执行失败: ' + (data.msg || '未知错误');
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '✗ 请求失败: ' + error.message;
            });
        }
    </script>
</body>
</html>
