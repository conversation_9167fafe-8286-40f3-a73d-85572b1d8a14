package com.service;

import com.model.Reserve;
import com.model.Mailremindertemplate;

/**
 * 邮件发送服务接口
 */
public interface EmailService {
    
    /**
     * 发送预约提醒邮件
     * @param toEmail 收件人邮箱
     * @param reserve 预约信息
     * @param template 邮件模板
     * @return 发送是否成功
     */
    boolean sendAppointmentReminder(String toEmail, Reserve reserve, Mailremindertemplate template);
    
    /**
     * 发送简单文本邮件
     * @param toEmail 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送是否成功
     */
    boolean sendSimpleEmail(String toEmail, String subject, String content);
}
