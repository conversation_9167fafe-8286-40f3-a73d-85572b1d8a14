<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 用户注册 </view>
    </diy-navbar>

        <form bindsubmit="submitForm" bindreset="resetForm" class="flex diy-form diy-col-24 justify-center">
            		<view class="diy-form-item diy-col-24">
			<view class="title"> 用户名： </view>
			<view class="input">
				<input class="flex1" name="lname" comfirm-type="='done'" type="text"  placeholder="请输入用户名"  />			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title"> 登录密码： </view>
			<view class="input">
				<input class="flex1" name="upassword"  password type="text"  placeholder="请输入登录密码" />
			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title"> 确认密码： </view>
			<view class="input">
				<input class="flex1" name="upassword2"  password type="text"  placeholder="请输入确认密码" />
			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title"> 姓名： </view>
			<view class="input">
				<input class="flex1" name="uname" comfirm-type="='done'" type="text"  placeholder="请输入姓名"  />			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title title-mb5"> 性别： </view>
			<view class="input">
				<radio-group class="flex flex-wrap check-green diy-col-24 justify-start" bindchange="changeRadio1" name="radio">
					<label wx:for="{{radioDatas1}}" wx:for-item="item" wx:for-index="index" data-key="index" class="diy-radio-label">
						<radio class="diy-radio" value="{{item.value}}" checked="{{item.checked}}"> </radio>
						<view class="diy-icon-radio {{item.checked?'checked':''}}"></view>
						<view class="diy-checkbox-text"> {{ item.label }} </view>
					</label>
				</radio-group>
				<input hidden type="hidden" name="usex" value="{{radioLabel1}}" />
			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title"> 手机号码： </view>
			<view class="input">
				<input class="flex1" name="uphone" comfirm-type="='done'" type="text"  placeholder="请输入手机号码"  />				<text class="diy-icon-mobilefill" style="color: #07c160"></text>
			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title"> 电子邮箱： </view>
			<view class="input">
				<input class="flex1" name="email" comfirm-type="='done'" type="text"  placeholder="请输入电子邮箱"  />			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title"> 家庭地址： </view>
			<view class="input">
				<input class="flex1" name="address" comfirm-type="='done'" type="text"  placeholder="请输入家庭地址" value="{{address}}"  />
				<view class="diy-tag radius-xs green" bindtap="onChangeAddress"> 获取 </view>
			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title"> 照片： </view>
			<view class="input flex">
					<view class="upload"  wx:if="{{filestr2!=''}}">
						<image class="image"  src="{{url}}{{filestr2}}"  mode="aspectFill"></image>
					</view>
					<view class="upload" catchtap="chooseImage2">
						<text class="diy-icon-cameraadd"></text>
					</view>
			</view>
		</view>

            <view class="flex diy-col-24 justify-center">
                <button style="" form-type="submit" class="diy-btn green flex1 margin-xs">提交注册</button>

                <button style="" form-type="reset" class="diy-btn green flex1 margin-xs">重置</button>
            </view>

        </form>
  
    <view class="clearfix"></view>
</view>





