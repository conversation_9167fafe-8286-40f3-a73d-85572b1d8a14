<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 我的挂号 </view>
    </diy-navbar>
    <block wx:for="{{msgs1}}" wx:key="k">
        <view class="grid rows">
            <view class="grid col-2">
                <view>科室：{{item.pname}}</view>
                <view>医生：{{item.by1}}</view>
                <view>预约日期：{{item.rdate}} {{item.rtime}}</view>
                <view>挂号费：<text class="text-red text-price">{{item.pmoney}}</text></view>
                <view>预约状态：{{item.flag}}</view>
            </view>
            <view class="diy-col-24 ">
             

                <view class="cu-tag bg-red fr" style="margin-left:10rpx" data-id="{{item.rid}}" catch:tap="dele"
                    wx:if="{{item.flag=='未支付'}}">取消</view>

                <text class="cu-tag fr bg-green" data-id="{{item.rid}}" data-je="{{item.pmoney}}" catch:tap="showModal2"
                    data-target="DialogModal2" wx:if="{{item.flag=='未支付'}}">支付</text>

                <view class="cu-tag bg-blue fr" bindtap="navigateTo" data-url="reserveShow" data-id="{{item.rid}}">查看
                </view>
            </view>
        </view>
    </block>
    <view class="clearfix"></view>
</view>


<view class="cu-modal {{modalName=='DialogModal2'?'show':''}}">
    <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
            <view class="content">支付挂号费</view>
            <view class="action" bindtap="hideModal">
                <text class="cuIcon-close text-red"></text>
            </view>
        </view>
        <view class="padding-xl">
            金额：<text class="text-red text-xl text-price">{{je}}</text>
        </view>
        <view class="cu-bar bg-white">
            <view class="action margin-0 flex-sub text-green " catch:tap="pay">
                <text class="cuIcon-moneybag"></text>微信支付
            </view>
            <view class="action margin-0 flex-sub text-green solid-left" bindtap="hideModal">取消</view>

        </view>
    </view>
</view>