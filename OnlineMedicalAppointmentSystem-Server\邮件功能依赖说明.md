# 邮件功能依赖说明

## 需要添加的jar包

为了使邮件提醒功能正常工作，需要在项目中添加以下jar包：

### 1. JavaMail API
- **文件名**: `javax.mail-1.6.2.jar`
- **下载地址**: https://repo1.maven.org/maven2/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar
- **放置位置**: `OnlineMedicalAppointmentSystem-Server/WebRoot/WEB-INF/lib/`

### 2. Activation API (如果需要)
- **文件名**: `activation-1.1.1.jar`
- **下载地址**: https://repo1.maven.org/maven2/javax/activation/activation/1.1.1/activation-1.1.1.jar
- **放置位置**: `OnlineMedicalAppointmentSystem-Server/WebRoot/WEB-INF/lib/`

## 安装步骤

1. 下载上述jar包
2. 将jar包复制到 `OnlineMedicalAppointmentSystem-Server/WebRoot/WEB-INF/lib/` 目录下
3. 重新编译和部署项目

## 邮件配置

邮件发送配置已在 `EmailServiceImpl.java` 中设置：
- 发件人邮箱: <EMAIL>
- SMTP服务器: smtp.qq.com
- 端口: 587
- 授权码: hoxowxwlcgbsfjag

## 定时任务配置

定时任务已配置为每12小时执行一次：
- Cron表达式: `0 0 */12 * * ?`
- 执行时间: 每天的0点和12点

## 功能说明

1. 系统会自动查询状态为"已支付"的预约记录
2. 根据邮件模板设置的提前天数计算目标日期
3. 向符合条件的患者发送邮件提醒
4. 邮件内容会根据模板动态替换占位符

## 模板占位符

邮件模板中可以使用以下占位符：
- `{预约日期}` - 预约日期
- `{预约时间}` - 预约时间段
- `{医生姓名}` - 医生姓名
- `{科室名称}` - 科室名称
- `{挂号费}` - 挂号费用
