{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\users\\UsersDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\users\\UsersDetail.vue", "mtime": 1749368892741}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgppbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vLi4vLi4vdXRpbHMvaHR0cCI7CmV4cG9ydCBkZWZhdWx0IHsKICAgIG5hbWU6ICdVc2Vyc0RldGFpbCcsCiAgICBjb21wb25lbnRzOiB7CiAgICB9LAogICAgZGF0YSgpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgICBpZDogJycsCiAgICAgICAgICAgIGZvcm1EYXRhOiB7fSwgLy/ooajljZXmlbDmja4gICAgICAgICAKCiAgICAgICAgfTsKICAgIH0sCiAgICBjcmVhdGVkKCkgewogICAgICAgIHRoaXMuaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsgLy/ojrflj5blj4LmlbAKICAgICAgICB0aGlzLmdldERhdGFzKCk7CiAgICB9LAoKCiAgICBtZXRob2RzOiB7CgogICAgICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICAgICAgZ2V0RGF0YXMoKSB7CiAgICAgICAgICAgIGxldCBwYXJhID0gewogICAgICAgICAgICB9OwogICAgICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3VzZXJzL2dldD9pZD0iICsgdGhpcy5pZDsKICAgICAgICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICB0aGlzLmZvcm1EYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyZXMucmVzZGF0YSkpOwogICAgICAgICAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAoKICAgICAgICAvLyDov5Tlm54KICAgICAgICBiYWNrKCkgewogICAgICAgICAgICAvL+i/lOWbnuS4iuS4gOmhtQogICAgICAgICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgICAgIH0sCgogICAgfSwKfQoK"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\users\\UsersDetail.vue"], "names": [], "mappings": ";;AAiCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACH,CAAC,CAAC,EAAE,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvB,CAAC;IACL,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;;;IAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;QAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;YACX,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC;QACN,CAAC;;QAED,CAAC,EAAE,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;;IAEL,CAAC;AACL", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/users/UsersDetail.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n        <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\n            <el-form-item label=\"用户名\">\n                {{ formData.lname }}</el-form-item>\n            <el-form-item label=\"登录密码\">\n                {{ formData.upassword }}</el-form-item>\n            <el-form-item label=\"姓名\">\n                {{ formData.uname }}</el-form-item>\n            <el-form-item label=\"性别\">\n                {{ formData.usex }}</el-form-item>\n            <el-form-item label=\"手机号码\">\n                {{ formData.uphone }}</el-form-item>\n                \n            <el-form-item label=\"家庭地址\">\n                {{ formData.address }}</el-form-item>\n            <el-form-item label=\"照片\" prop=\"pic\">\n                <img :src=\"'http://localhost:8088/OnlineMedicalAppointmentSystem_Server/upload/' + formData.pic\"\n                    style=\"width: 150px;height: 150px\" />\n            </el-form-item>\n            <el-form-item label=\"注册时间\">\n                {{ formData.utime }}</el-form-item>\n            <el-form-item>\n                <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\n            </el-form-item>\n        </el-form>\n\n\n    </div>\n</template>\n\n<script>\n\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n    name: 'UsersDetail',\n    components: {\n    },\n    data() {\n        return {\n            id: '',\n            formData: {}, //表单数据         \n\n        };\n    },\n    created() {\n        this.id = this.$route.query.id; //获取参数\n        this.getDatas();\n    },\n\n\n    methods: {\n\n        //获取列表数据\n        getDatas() {\n            let para = {\n            };\n            this.listLoading = true;\n            let url = base + \"/users/get?id=\" + this.id;\n            request.post(url, para).then((res) => {\n                this.formData = JSON.parse(JSON.stringify(res.resdata));\n                this.listLoading = false;\n            });\n        },\n\n        // 返回\n        back() {\n            //返回上一页\n            this.$router.go(-1);\n        },\n\n    },\n}\n\n</script>\n<style scoped></style>\n"]}]}