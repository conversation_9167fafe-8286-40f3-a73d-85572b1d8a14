package com.service;

import com.model.Reserve;
import com.model.Mailremindertemplate;
import com.model.Users;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 邮件提醒定时任务
 */
@Service
public class EmailReminderScheduler {
    
    @Autowired
    private ReserveService reserveService;
    
    @Autowired
    private MailremindertemplateService mailremindertemplateService;
    
    @Autowired
    private UsersService usersService;
    
    @Autowired
    private EmailService emailService;
    
    /**
     * 每12小时执行一次的邮件提醒任务
     * cron表达式: 0 0 */
    @Scheduled(cron = "0 0 */12 * * ?")
    public void sendAppointmentReminders() {
        try {
            System.out.println("开始执行邮件提醒任务: " + new Date());
            
            // 获取所有邮件模板
            List<Mailremindertemplate> templates = mailremindertemplateService.queryMailremindertemplateList(null, null);
            
            if (templates == null || templates.isEmpty()) {
                System.out.println("没有找到邮件模板，跳过邮件发送");
                return;
            }
            
            // 遍历每个邮件模板
            for (Mailremindertemplate template : templates) {
                processTemplateReminders(template);
            }
            
            System.out.println("邮件提醒任务执行完成: " + new Date());
            
        } catch (Exception e) {
            System.err.println("邮件提醒任务执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理单个模板的提醒
     */
    private void processTemplateReminders(Mailremindertemplate template) {
        try {
            // 检查模板的提前天数
            Integer days = template.getDays();
            if (days == null) {
                System.out.println("模板提前天数为空，跳过处理，模板ID: " + template.getMid());
                return;
            }

            // 计算目标日期（当前日期 + 提前天数）
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, days);
            String targetDate = new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime());

            System.out.println("处理模板ID: " + template.getMid() +
                             ", 提前天数: " + days +
                             ", 目标日期: " + targetDate);

            // 查询已支付且预约日期匹配的预约记录
            Reserve queryReserve = new Reserve();
            queryReserve.setFlag("已支付"); // 预约状态为已支付
            queryReserve.setRdate(targetDate); // 预约日期

            List<Reserve> reserves = reserveService.queryReserveList(queryReserve, null);

            if (reserves == null || reserves.isEmpty()) {
                System.out.println("没有找到符合条件的预约记录，模板ID: " + template.getMid());
                return;
            }

            System.out.println("找到 " + reserves.size() + " 条符合条件的预约记录");

            // 遍历预约记录，发送邮件
            for (Reserve reserve : reserves) {
                sendReminderEmail(reserve, template);
            }

        } catch (Exception e) {
            System.err.println("处理模板提醒失败，模板ID: " + template.getMid() + ", 错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 发送单个提醒邮件
     */
    private void sendReminderEmail(Reserve reserve, Mailremindertemplate template) {
        try {
            // 根据用户名查询用户信息获取邮箱
            Users queryUser = new Users();
            queryUser.setLname(reserve.getLname());
            
            List<Users> users = usersService.queryUsersList(queryUser, null);
            
            if (users == null || users.isEmpty()) {
                System.out.println("未找到用户信息，用户名: " + reserve.getLname());
                return;
            }
            
            Users user = users.get(0);
            String email = user.getEmail();
            
            if (email == null || email.trim().isEmpty()) {
                System.out.println("用户邮箱为空，用户名: " + reserve.getLname());
                return;
            }
            
            // 发送邮件
            boolean success = emailService.sendAppointmentReminder(email, reserve, template);
            
            if (success) {
                System.out.println("邮件发送成功 - 用户: " + reserve.getLname() + 
                                 ", 邮箱: " + email + 
                                 ", 预约日期: " + reserve.getRdate());
            } else {
                System.err.println("邮件发送失败 - 用户: " + reserve.getLname() + 
                                 ", 邮箱: " + email);
            }
            
        } catch (Exception e) {
            System.err.println("发送提醒邮件失败，预约ID: " + reserve.getRid() + ", 错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
