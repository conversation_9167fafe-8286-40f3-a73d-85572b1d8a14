package com.service;

import com.model.Reserve;
import com.model.Mailremindertemplate;
import com.model.Users;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 邮件提醒定时任务
 */
@Service
public class EmailReminderScheduler {
    
    @Autowired
    private ReserveService reserveService;
    
    @Autowired
    private MailremindertemplateService mailremindertemplateService;
    
    @Autowired
    private UsersService usersService;
    
    @Autowired
    private EmailService emailService;
    

    @Scheduled(cron = "0 0 */12 * * ?")
    public void sendAppointmentReminders() {
        try {
            System.out.println("=== 开始执行邮件提醒任务: " + new Date() + " ===");

            // 获取所有邮件模板
            List<Mailremindertemplate> templates = mailremindertemplateService.queryMailremindertemplateList(null, null);

            System.out.println("查询到邮件模板数量: " + (templates != null ? templates.size() : 0));

            if (templates == null || templates.isEmpty()) {
                System.out.println("没有找到邮件模板，跳过邮件发送");
                return;
            }

            // 打印所有模板信息
            for (Mailremindertemplate template : templates) {
                System.out.println("模板信息 - ID: " + template.getMid() +
                                 ", 提前天数: " + template.getDays() +
                                 ", 内容长度: " + (template.getContent() != null ? template.getContent().length() : 0));
            }

            // 遍历每个邮件模板
            for (Mailremindertemplate template : templates) {
                processTemplateReminders(template);
            }

            System.out.println("=== 邮件提醒任务执行完成: " + new Date() + " ===");

        } catch (Exception e) {
            System.err.println("邮件提醒任务执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理单个模板的提醒
     */
    private void processTemplateReminders(Mailremindertemplate template) {
        try {
            System.out.println("--- 开始处理模板 ---");
            System.out.println("模板ID: " + template.getMid());

            // 检查模板的提前天数
            Integer days = template.getDays();
            if (days == null) {
                System.out.println("模板提前天数为空，跳过处理，模板ID: " + template.getMid());
                return;
            }

            System.out.println("提前天数: " + days);

            // 计算目标日期（当前日期 + 提前天数）
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, days);
            String targetDate = new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime());

            // 同时计算今天的日期用于对比
            String todayDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

            System.out.println("今天日期: " + todayDate);
            System.out.println("目标预约日期: " + targetDate);

            // 查询已支付且预约日期匹配的预约记录
            Reserve queryReserve = new Reserve();
            queryReserve.setFlag("已支付"); // 预约状态为已支付
            queryReserve.setRdate(targetDate); // 预约日期

            System.out.println("查询条件 - 状态: 已支付, 预约日期: " + targetDate);

            List<Reserve> reserves = reserveService.queryReserveList(queryReserve, null);

            System.out.println("查询结果: " + (reserves != null ? reserves.size() : 0) + " 条记录");

            if (reserves == null || reserves.isEmpty()) {
                System.out.println("没有找到符合条件的预约记录，模板ID: " + template.getMid());

                // 额外查询：看看有没有任何已支付的记录
                Reserve allPaidReserve = new Reserve();
                allPaidReserve.setFlag("已支付");
                List<Reserve> allPaidReserves = reserveService.queryReserveList(allPaidReserve, null);
                System.out.println("所有已支付的预约记录数量: " + (allPaidReserves != null ? allPaidReserves.size() : 0));

                if (allPaidReserves != null && !allPaidReserves.isEmpty()) {
                    System.out.println("已支付预约记录的日期列表:");
                    for (Reserve r : allPaidReserves) {
                        System.out.println("  预约ID: " + r.getRid() + ", 日期: " + r.getRdate() + ", 用户: " + r.getLname());
                    }
                }
                return;
            }

            System.out.println("找到 " + reserves.size() + " 条符合条件的预约记录");

            // 打印找到的预约记录详情
            for (Reserve reserve : reserves) {
                System.out.println("预约记录 - ID: " + reserve.getRid() +
                                 ", 用户: " + reserve.getLname() +
                                 ", 日期: " + reserve.getRdate() +
                                 ", 状态: " + reserve.getFlag());
            }

            // 遍历预约记录，发送邮件
            for (Reserve reserve : reserves) {
                sendReminderEmail(reserve, template);
            }

        } catch (Exception e) {
            System.err.println("处理模板提醒失败，模板ID: " + template.getMid() + ", 错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 发送单个提醒邮件
     */
    private void sendReminderEmail(Reserve reserve, Mailremindertemplate template) {
        try {
            System.out.println("--- 开始发送邮件 ---");
            System.out.println("预约ID: " + reserve.getRid() + ", 用户名: " + reserve.getLname());

            // 根据用户名查询用户信息获取邮箱
            Users queryUser = new Users();
            queryUser.setLname(reserve.getLname());

            List<Users> users = usersService.queryUsersList(queryUser, null);

            System.out.println("查询用户结果: " + (users != null ? users.size() : 0) + " 条记录");

            if (users == null || users.isEmpty()) {
                System.out.println("未找到用户信息，用户名: " + reserve.getLname());
                return;
            }

            Users user = users.get(0);
            String email = user.getEmail();

            System.out.println("用户邮箱: " + email);

            if (email == null || email.trim().isEmpty()) {
                System.out.println("用户邮箱为空，用户名: " + reserve.getLname());
                return;
            }

            System.out.println("准备发送邮件到: " + email);

            // 发送邮件
            boolean success = emailService.sendAppointmentReminder(email, reserve, template);

            if (success) {
                System.out.println("✓ 邮件发送成功 - 用户: " + reserve.getLname() +
                                 ", 邮箱: " + email +
                                 ", 预约日期: " + reserve.getRdate());
            } else {
                System.err.println("✗ 邮件发送失败 - 用户: " + reserve.getLname() +
                                 ", 邮箱: " + email);
            }

        } catch (Exception e) {
            System.err.println("发送提醒邮件失败，预约ID: " + reserve.getRid() + ", 错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试特定模板ID的邮件发送
     * @param templateId 模板ID
     */
    public void testEmailReminderByTemplateId(Integer templateId) {
        try {
            System.out.println("=== 测试模板ID " + templateId + " 的邮件发送 ===");

            // 获取指定模板
            Mailremindertemplate template = mailremindertemplateService.queryMailremindertemplateById(templateId);

            if (template == null) {
                System.out.println("未找到模板ID: " + templateId);
                return;
            }

            System.out.println("找到模板 - ID: " + template.getMid() +
                             ", 提前天数: " + template.getDays() +
                             ", 内容长度: " + (template.getContent() != null ? template.getContent().length() : 0));

            // 处理该模板的提醒
            processTemplateReminders(template);

            System.out.println("=== 测试完成 ===");

        } catch (Exception e) {
            System.err.println("测试邮件发送失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试向指定用户发送邮件
     * @param username 用户名
     * @param templateId 模板ID
     */
    public void testEmailToUser(String username, Integer templateId) {
        try {
            System.out.println("=== 测试向用户 " + username + " 发送邮件 ===");

            // 获取模板
            Mailremindertemplate template = mailremindertemplateService.queryMailremindertemplateById(templateId);
            if (template == null) {
                System.out.println("未找到模板ID: " + templateId);
                return;
            }

            // 查询用户的预约记录
            Reserve queryReserve = new Reserve();
            queryReserve.setLname(username);
            queryReserve.setFlag("已支付");

            List<Reserve> reserves = reserveService.queryReserveList(queryReserve, null);

            if (reserves == null || reserves.isEmpty()) {
                System.out.println("用户 " + username + " 没有已支付的预约记录");
                return;
            }

            System.out.println("找到用户 " + username + " 的 " + reserves.size() + " 条已支付预约记录");

            // 发送邮件给第一条记录
            Reserve reserve = reserves.get(0);
            sendReminderEmail(reserve, template);

            System.out.println("=== 测试完成 ===");

        } catch (Exception e) {
            System.err.println("测试发送邮件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
