package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.UsersMapper;
import com.model.Users;
import com.util.PageBean;
@Service
public class UsersServiceImpl implements UsersService{
        
	@Autowired
	private UsersMapper usersMapper;

	//查询多条记录
	public List<Users> queryUsersList(Users users,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(users, page);
		
		List<Users> getUsers = usersMapper.query(map);
		
		return getUsers;
	}
	
	//得到记录总数
	@Override
	public int getCount(Users users) {
		Map<String, Object> map = getQueryMap(users, null);
		int count = usersMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Users users,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(users!=null){
			map.put("lname", users.getLname());
			map.put("upassword", users.getUpassword());
			map.put("uname", users.getUname());
			map.put("usex", users.getUsex());
			map.put("uphone", users.getUphone());
			map.put("email", users.getEmail());
			map.put("address", users.getAddress());
			map.put("pic", users.getPic());
			map.put("utime", users.getUtime());
			map.put("sort", users.getSort());
			map.put("condition", users.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertUsers(Users users) throws Exception {
		return usersMapper.insertUsers(users);
	}

	//根据ID删除
	public int deleteUsers(String id) throws Exception {
		return usersMapper.deleteUsers(id);
	}

	//更新
	public int updateUsers(Users users) throws Exception {
		return usersMapper.updateUsers(users);
	}
	
	//根据ID得到对应的记录
	public Users queryUsersById(String id) throws Exception {
		Users po =  usersMapper.queryUsersById(id);
		return po;
	}
}

