# 邮件问题诊断指南

## 问题现象
有符合条件的预约记录，但是邮件没有发送。

## 诊断步骤

### 1. 检查邮件模板
确认邮件模板ID为4的记录存在且配置正确：
```sql
SELECT * FROM mailremindertemplate WHERE mid = 4;
```

### 2. 检查预约记录
确认有状态为"已支付"的预约记录：
```sql
SELECT * FROM reserve WHERE flag = '已支付';
```

### 3. 检查用户邮箱
确认用户表中有邮箱信息：
```sql
SELECT lname, email FROM users WHERE lname IN (
    SELECT DISTINCT lname FROM reserve WHERE flag = '已支付'
);
```

### 4. 检查日期匹配
根据模板的提前天数，检查是否有匹配的预约日期：
- 模板提前天数：1天
- 当前日期：2024-XX-XX
- 目标预约日期：2024-XX-XX（当前日期+1天）

```sql
-- 假设今天是2024-06-07，那么应该查找2024-06-08的预约
SELECT * FROM reserve 
WHERE flag = '已支付' 
AND rdate = '2024-06-08';
```

## 测试方法

### 方法1: 使用测试页面
1. 访问 `/email-test.html`
2. 点击"测试特定模板邮件发送"
3. 输入模板ID: 4
4. 查看服务器控制台日志

### 方法2: 使用API直接测试
```
POST /api/email/test-template
Content-Type: application/x-www-form-urlencoded

templateId=4
```

### 方法3: 测试特定用户
```
POST /api/email/test-user
Content-Type: application/x-www-form-urlencoded

username=user003&templateId=4
```

## 常见问题及解决方案

### 1. 找不到邮件模板
**现象**: 日志显示"没有找到邮件模板"
**解决**: 检查数据库中是否存在模板记录

### 2. 找不到符合条件的预约记录
**现象**: 日志显示"没有找到符合条件的预约记录"
**可能原因**:
- 预约状态不是"已支付"
- 预约日期不匹配（日期计算错误）
- 数据库中没有符合条件的记录

### 3. 找不到用户信息
**现象**: 日志显示"未找到用户信息"
**解决**: 检查users表中是否有对应的用户记录

### 4. 用户邮箱为空
**现象**: 日志显示"用户邮箱为空"
**解决**: 在users表中为用户添加邮箱地址

### 5. 邮件发送失败
**现象**: 日志显示"邮件发送失败"
**可能原因**:
- 缺少JavaMail jar包
- 网络连接问题
- SMTP配置错误
- 邮箱授权码错误

## 调试日志说明

正常的日志输出应该包含：
```
=== 开始执行邮件提醒任务: [时间] ===
查询到邮件模板数量: 1
模板信息 - ID: 4, 提前天数: 1, 内容长度: [数字]
--- 开始处理模板 ---
模板ID: 4
提前天数: 1
今天日期: 2024-06-07
目标预约日期: 2024-06-08
查询条件 - 状态: 已支付, 预约日期: 2024-06-08
查询结果: [数字] 条记录
找到 [数字] 条符合条件的预约记录
--- 开始发送邮件 ---
预约ID: [数字], 用户名: [用户名]
查询用户结果: 1 条记录
用户邮箱: [邮箱地址]
准备发送邮件到: [邮箱地址]
--- EmailService: 开始发送预约提醒邮件 ---
✓ 邮件发送成功
```

## 立即测试建议

1. **先测试邮件发送功能**:
   - 使用"发送测试邮件"功能，确认基本的邮件发送是否正常

2. **测试特定模板**:
   - 使用模板ID 4进行测试，查看详细日志

3. **检查数据匹配**:
   - 确认预约日期是否与计算的目标日期匹配

4. **验证用户邮箱**:
   - 确认user003用户是否有邮箱地址

## 临时解决方案

如果需要立即测试，可以：
1. 修改预约记录的日期，使其匹配计算的目标日期
2. 或者修改模板的提前天数，使其匹配现有的预约日期
3. 确保用户表中有有效的邮箱地址
