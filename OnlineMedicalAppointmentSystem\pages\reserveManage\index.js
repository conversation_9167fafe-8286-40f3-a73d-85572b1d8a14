const App = getApp();
Page({
  data: {
    msgs1: [],
    url: App.Config.basePath,
    day: App.Tools.getNowFormatDate(),
  },

  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.getMsgs1();
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },

  //获取数据列表
  getMsgs1() {
    let param = {
      f: 1,
      lname: wx.getStorageSync("lname"),
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/reserve/list2.action?currentPage=1&pageSize=100").then((data) => {
      this.setData({
        msgs1: data.resdata,
      });
    });
  },

  //删除
  dele(e) {
    var that = this;
    const dataset = e.currentTarget.dataset;
    let param = {};
    App.WxService.showModal({
      title: "友情提示",
      content: "您确定要取消再预约吗？",
    }).then((data) => {
      if (data.confirm == 1) {
        let param = {
          rid: dataset.id,
        };
        App.HttpService.delData(param, "/reserve/del.action?id=" + dataset.id).then((data) => {
          App.WxService.showToast({
            title: "取消成功!",
            icon: "success",
            duration: 1500,
          });
          setTimeout(function () {
            that.getMsgs1();
          }, 1500);
        });
      }
    });
  },

  showModal2(e) {
    this.setData({
      modalName: e.currentTarget.dataset.target,
      id: e.currentTarget.dataset.id,
      je: e.currentTarget.dataset.je,
    });
  },
  hideModal(e) {
    this.setData({
      modalName: null,
    });
  },

  pay(e) {
    var that = this;
    var data = null;

    data = App.Tools.extend(data, {
      f: 1,
      rid: this.data.id,
      flag: "已支付",
    });

    App.HttpService.saveData(data, "/reserve/update2.action").then((data) => {
      App.WxService.showToast({
        title: "支付成功!",
        icon: "success",
        duration: 1500,
      });
      setTimeout(function () {
        that.setData({
          modalName: null,
        });
        that.getMsgs1();
      }, 1500);
    });
  },

});
