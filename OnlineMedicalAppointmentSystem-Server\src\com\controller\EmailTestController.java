package com.controller;

import com.response.Response;
import com.service.EmailReminderScheduler;
import com.service.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 邮件测试控制器
 */
@RestController
@RequestMapping("/api/email")
public class EmailTestController {
    
    @Autowired
    private EmailReminderScheduler emailReminderScheduler;
    
    @Autowired
    private EmailService emailService;
    
    /**
     * 手动触发邮件提醒任务
     */
    @ResponseBody
    @PostMapping(value = "/trigger-reminder")
    @CrossOrigin
    public Response triggerEmailReminder(HttpServletRequest req) {
        try {
            System.out.println("手动触发邮件提醒任务");
            emailReminderScheduler.sendAppointmentReminders();
            return Response.success("邮件提醒任务执行完成");
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(500, "邮件提醒任务执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送测试邮件
     */
    @ResponseBody
    @PostMapping(value = "/send-test")
    @CrossOrigin
    public Response sendTestEmail(HttpServletRequest req) {
        try {
            String toEmail = req.getParameter("email");
            if (toEmail == null || toEmail.trim().isEmpty()) {
                return Response.error(400, "请提供收件人邮箱地址");
            }
            
            String subject = "【丽江医院】邮件功能测试";
            String content = "<h2>邮件功能测试</h2>" +
                           "<p>这是一封测试邮件，用于验证邮件发送功能是否正常。</p>" +
                           "<p>发送时间: " + new java.util.Date() + "</p>" +
                           "<p>如果您收到这封邮件，说明邮件功能配置正确。</p>";
            
            boolean success = emailService.sendSimpleEmail(toEmail, subject, content);
            
            if (success) {
                return Response.success("测试邮件发送成功");
            } else {
                return Response.error(500, "测试邮件发送失败");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(500, "发送测试邮件时出错: " + e.getMessage());
        }
    }
    
    /**
     * 获取邮件发送状态
     */
    @ResponseBody
    @GetMapping(value = "/status")
    @CrossOrigin
    public Response getEmailStatus() {
        try {
            return Response.success("邮件服务运行正常");
        } catch (Exception e) {
            return Response.error(500, "邮件服务异常: " + e.getMessage());
        }
    }
}
